#!/usr/bin/env python3

"""
Simple test to verify that our fix for handling None transcription_copilot_duration_threshold works.
"""

import pandas as pd
import sys
import os

# Add the source path to sys.path so we can import the modules
sys.path.insert(0, 'src/py/tasks/integration/integration-audio-comms-tasks')

from integration_audio_comms_tasks.transcription.transcription_copilot.input_schema import TranscriptionCopilotStaticConfig
from integration_audio_comms_tasks.transcription.transcription_copilot.transcription_copilot_flow import filter_transcripts_by_duration

def test_none_duration_threshold():
    """Test that filter_transcripts_by_duration handles None threshold correctly."""
    
    # Create test data
    call_data = {
        'voiceFile.fileInfo.location.bucket': ['test-bucket'],
        'voiceFile.fileInfo.location.key': ['test-key'],
        'callDuration': ['00:01:30']  # 1 minute 30 seconds
    }
    call_frame = pd.DataFrame(call_data)
    
    transcript_data = {
        'recordingSourceKey': ['s3://test-bucket/test-key'],
        'content': ['Test transcript content']
    }
    transcript_frame = pd.DataFrame(transcript_data)
    
    # Create static config with None threshold
    static_config = TranscriptionCopilotStaticConfig(transcription_copilot_duration_threshold=None)
    
    print(f"Static config threshold: {static_config.transcription_copilot_duration_threshold}")
    
    # Test the function
    try:
        result_call_frame, result_transcript_frame = filter_transcripts_by_duration(
            call_frame=call_frame,
            transcript_frame=transcript_frame,
            cloud_provider_prefix="s3://",
            static_config=static_config,
            file_based=True
        )
        
        print("✅ SUCCESS: filter_transcripts_by_duration handled None threshold correctly")
        print(f"Input call frame shape: {call_frame.shape}")
        print(f"Output call frame shape: {result_call_frame.shape}")
        print(f"Input transcript frame shape: {transcript_frame.shape}")
        print(f"Output transcript frame shape: {result_transcript_frame.shape}")
        
        # Verify that the frames are returned unchanged (since threshold is None, no filtering should occur)
        assert result_call_frame.shape == call_frame.shape, "Call frame should be unchanged when threshold is None"
        assert result_transcript_frame.shape == transcript_frame.shape, "Transcript frame should be unchanged when threshold is None"
        
        print("✅ All assertions passed!")
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_threshold():
    """Test that filter_transcripts_by_duration works correctly with a valid threshold."""
    
    # Create test data
    call_data = {
        'voiceFile.fileInfo.location.bucket': ['test-bucket'],
        'voiceFile.fileInfo.location.key': ['test-key'],
        'callDuration': ['00:01:30']  # 1 minute 30 seconds = 90 seconds
    }
    call_frame = pd.DataFrame(call_data)
    
    transcript_data = {
        'recordingSourceKey': ['s3://test-bucket/test-key'],
        'content': ['Test transcript content']
    }
    transcript_frame = pd.DataFrame(transcript_data)
    
    # Create static config with threshold of 60 seconds (call should pass)
    static_config = TranscriptionCopilotStaticConfig(transcription_copilot_duration_threshold=60)
    
    print(f"Static config threshold: {static_config.transcription_copilot_duration_threshold}")
    
    # Test the function
    try:
        result_call_frame, result_transcript_frame = filter_transcripts_by_duration(
            call_frame=call_frame,
            transcript_frame=transcript_frame,
            cloud_provider_prefix="s3://",
            static_config=static_config,
            file_based=True
        )
        
        print("✅ SUCCESS: filter_transcripts_by_duration handled valid threshold correctly")
        print(f"Input call frame shape: {call_frame.shape}")
        print(f"Output call frame shape: {result_call_frame.shape}")
        print(f"Input transcript frame shape: {transcript_frame.shape}")
        print(f"Output transcript frame shape: {result_transcript_frame.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing transcription_copilot_duration_threshold fix...")
    print("=" * 60)
    
    print("\n1. Testing with None threshold:")
    test1_passed = test_none_duration_threshold()
    
    print("\n2. Testing with valid threshold:")
    test2_passed = test_with_threshold()
    
    print("\n" + "=" * 60)
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED! The fix is working correctly.")
        sys.exit(0)
    else:
        print("💥 SOME TESTS FAILED!")
        sys.exit(1)
