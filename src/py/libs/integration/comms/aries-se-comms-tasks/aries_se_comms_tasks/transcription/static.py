from aries_se_comms_tasks.transcription.deepgram.static import DeepGramLanguageEnum
from aries_se_comms_tasks.transcription.deepgram.static import DeepGramLanguageEnum as DLE
from aries_se_comms_tasks.transcription.transcriber.static import TranscriberTargetFields
from enum import Enum
from se_data_lake.lake_path import get_base_ingress_depository_lake_path_for_transcription
from se_elastic_schema.static.transcript import Language<PERSON>ode<PERSON><PERSON>, VendorEnum
from se_elastic_schema.static.transcript import LanguageEnum as LE

TRANSCRIPTION_USAGE_PATH = f"{get_base_ingress_depository_lake_path_for_transcription()}usage/"
TRANSCRIPTION_USAGE_FILE = "usage_in_seconds.json"


class TranscriptModelFields:
    ANALYTICS = "analytics"
    CLASS = "class_"
    CLASSIFIER = "classifier"
    CODE = "code"
    COMPLEXITY_METHOD = "complexity.method"
    COMPLEXITY_SCORE = "complexity.score"
    CONFIDENCE = "confidence"
    COUNT_DISTINCT_TOKENS = "count.distinctTokens"
    COUNT_SPEAKERS = "count.speakers"
    COUNT_TOKENS = "count.tokens"
    DATETIME_JOB_SUBMITTED = "dateTime.jobSubmitted"
    DATETIME_TRANSCRIPTION_DURATION_UNIT = "dateTime.transcriptionDuration.unit"
    DATETIME_TRANSCRIPTION_DURATION_VALUE = "dateTime.transcriptionDuration.value"
    DATETIME_TRANSCRIPTION_END = "dateTime.transcriptionEnd"
    DATETIME_TRANSCRIPTION_START = "dateTime.transcriptionStart"
    DURATION = "duration"
    ENTITIES = "entities"
    ID = "id"
    INDEX = "index"
    IS_ANNOTATED = "isAnnotated"
    IS_TRANSCRIBED_BY_STEELEYE = "isTranscribedBySteelEye"
    JOB_ID = "jobId"
    LABEL = "label"
    LANGUAGE = "language"
    LANGUAGES = "languages"
    MODEL = "model"
    MODEL_ID = "model.id"
    MODEL_LANGUAGE = "model.language"
    MODEL_VENDOR = "model.vendor"
    NUMBER_OF_LANGUAGES = "numberOfLanguages"
    OCCURRENCE = "occurrence"
    OVERALL_SENTIMENT = "overallSentiment"
    OVERALL_SENTIMENT_CONFIDENCE = "overallSentimentConfidence"
    OVERALL_SENTIMENT_RATIO = "overallSentimentRatio"
    PERCENTAGE = "percentage"
    PREDICTED_CLASSES = "predictedClasses"
    PREDICTIONS = "predictions"
    QUESTIONS = "questions"
    RECORDING_SOURCE_KEY = "recordingSourceKey"
    SEGMENTS = "segments"
    SENTIMENT = "sentiment"
    SENTIMENT_DETAILS = "sentimentDetails"
    SOURCE_KEY = "sourceKey"
    SPEAKER = "speaker"
    SPEAKER_ID = "speakerId"
    SPEAKER_USER_ID = "speakerUserId"
    SPEAKERS = "speakers"
    SUMMARY = "summary"
    TARGET_LANGUAGE = "targetLanguage"
    TEXT = "text"
    TIME_DURATION = "time.duration"
    TIME_END = "time.end"
    TIME_START = "time.start"
    TOKEN = "token"
    TOKEN_TYPE = "tokenType"
    TOKENS = "tokens"
    TOPICS = "topics"
    TRANSCRIPT_SOURCE_KEY = "transcriptSourceKey"
    TRANSLATIONS = "translations"
    VALUE = "value"
    VENDOR = "vendor"


class TranscriptionFields:
    ALTERNATIVES = "alternatives"
    ATTACHMENT_BUCKET = "voiceFile.fileInfo.location.bucket"
    ATTACHMENT_FILE_TYPE = "voiceFile.fileType"
    ATTACHMENT_KEY = "voiceFile.fileInfo.location.key"
    AUTO = "auto"
    BODY = "body"
    BODY_DISPLAY_TEXT = "body.displayText"
    BODY_TEXT = "body.text"
    BODY_TYPE = "body.type"
    CONFIDENCE = "confidence"
    DESCRIPTION = "description"
    DURATION = "callDuration"
    END = "end"
    FROM = "identifiers.fromId"
    IDENTIFIERS_FROM_ID = "identifiers.fromId"
    LANGUAGE = "language"
    LANGUAGE_OPTIONS = "LanguageOptions"
    METADATA_CREATED = "metadata.created"
    METADATA_REQUEST_ID = "metadata.request_id"
    MODEL = "Call"
    PARTICIPANTS = "participants"
    PUNCTUATED_WORD = "punctuated_word"
    RECORD_ID = "&id"
    RECORD_KEY = "&key"
    RESULTS_CHANNELS = "results.channels"
    SOURCE_AUDIO_LANGUAGE = "sourceAudioLanguage"
    SPEAKER = "speaker"
    START = "start"
    STATUS = "status"
    TEXT = "body.text"
    TIME = "time"
    TO = "identifiers.toIds"
    TRANSCRIBED = "transcribed"
    TRANSCRIPT = "transcript"
    TRANSCRIPTION = "transcription"
    TRANSCRIPTION_SCHEDULE = "transcription_schedule"
    TRANSCRIPTION_STATUS = "transcriptionStatus"
    TRANSCRIPTION_STATUS_DESCRIPTION = "transcriptionStatus.description"
    TRANSCRIPTS = "transcripts"
    TRANSLATED_TEXT = "TranslatedText"
    TRANSLATION = "translation"
    VENDOR = "vendor"
    WORD = "word"
    WORDS = "words"


class TranscriptionSegmentFields:
    AGGREGATED_SEGMENT_SENTIMENT = "aggregatedSegmentSentiment"
    AGGREGATED_SEGMENT_SENTIMENT_CONFIDENCE = "aggregatedSegmentSentimentConfidence"
    AGGREGATED_SEGMENT_SENTIMENT_RATIO = "aggregatedSegmentSentimentRatio"
    LANGUAGE = "language"
    SEGMENT_CONFIDENCE = "segmentConfidence"
    SEGMENT_SENTIMENT_DETAILS = "segmentSentimentDetails"
    TEXT = "text"
    TRANSLATED_LANGUAGE = "translatedLanguage"
    TRANSLATED_TEXT = "translatedText"


class TranscriptionSpeakerFields:
    ID = "id"
    PERCENTAGE = "percentage"


class TranscriptionLanguageFields:
    CODE = "code"
    PERCENTAGE = "percentage"


class TranscriptionSentimentDetails:
    CONFIDENCE = "confidence"
    RATIO = "ratio"
    SENTIMENT_TYPE = "sentimentType"


class AWSTranscribeFields:
    BODY = "body"
    LANGUAGECODE = "LanguageCode"
    MEDIAFILEURI = "MediaFileUri"
    RESULTS = "results"
    TRANSCRIPT = "transcript"
    TRANSCRIPTIONJOB = "TranscriptionJob"
    TRANSCRIPTS = "transcripts"
    RESULTLANGUAGE = "language_code"


class AWSS3Fields:
    BUCKET = "Bucket"
    CONTENTS = "Contents"
    KEY = "Key"
    PARAM = "s3"


class AWSDynamoFields:
    BUCKET = "bucket"
    ITEMS = "Items"
    PARAM = "dynamo"
    PATH = "path"


class AWSTranslationLanguageCodesEnum(str, Enum):
    AFRIKAANS = "af"
    ALBANIAN = "sq"
    AMHARIC = "am"
    ARABIC = "ar"
    ARMENIAN = "hy"
    AZERBAIJANI = "az"
    BENGALI = "bn"
    BOSNIAN = "bs"
    BULGARIAN = "bg"
    CATALAN = "ca"
    CHINESE_SIMPLIFIED = "zh"
    CHINESE_TRADITIONAL = "zh-TW"
    CROATIAN = "hr"
    CZECH = "cs"
    DANISH = "da"
    DARI = "fa-AF"
    DUTCH = "nl"
    ENGLISH = "en"
    ESTONIAN = "et"
    FARSI_PERSIAN = "fa"
    FILIPINO_TAGALOG = "tl"
    FINNISH = "fi"
    FRENCH = "fr"
    FRENCH_CANADA = "fr-CA"
    GEORGIAN = "ka"
    GERMAN = "de"
    GREEK = "el"
    GUJARATI = "gu"
    HAITIAN = "ht"
    HAUSA = "ha"
    HEBREW = "he"
    HINDI = "hi"
    HUNGARIAN = "hu"
    ICELANDIC = "is"
    INDONESIAN = "id"
    IRISH = "ga"
    ITALIAN = "it"
    JAPANESE = "ja"
    KANNADA = "kn"
    KAZAKH = "kk"
    KOREAN = "ko"
    LATVIAN = "lv"
    LITHUANIAN = "lt"
    MACEDONIAN = "mk"
    MALAY = "ms"
    MALAYALAM = "ml"
    MALTESE = "mt"
    MARATHI = "mr"
    MONGOLIAN = "mn"
    NORWEGIAN = "no"
    PASHTO = "ps"
    POLISH = "pl"
    PORTUGUESE = "pt"
    PORTUGUESE_PORTUGAL = "pt-PT"
    PUNJABI = "pa"
    ROMANIAN = "ro"
    RUSSIAN = "ru"
    SERBIAN = "sr"
    SINHALA = "si"
    SLOVAK = "sk"
    SLOVENIAN = "sl"
    SOMALI = "so"
    SPANISH = "es"
    SPANISH_MEXICO = "es-MX"
    SWAHILI = "sw"
    SWEDISH = "sv"
    TAMIL = "ta"
    TELUGU = "te"
    THAI = "th"
    TURKISH = "tr"
    UKRAINIAN = "uk"
    URDU = "ur"
    UZBEK = "uz"
    VIETNAMESE = "vi"
    WELSH = "cy"


DEEPGRAM_LANGUAGE_CODE_TO_AMAZON_LANGUAGE_CODE = {
    DLE.BULGARIAN: AWSTranslationLanguageCodesEnum.BULGARIAN,
    DLE.CATALAN: AWSTranslationLanguageCodesEnum.CATALAN,
    DLE.CHINESE: AWSTranslationLanguageCodesEnum.CHINESE_SIMPLIFIED,
    DLE.CHINESE_CHINA: AWSTranslationLanguageCodesEnum.CHINESE_SIMPLIFIED,
    DLE.CHINESE_TAIWAN: AWSTranslationLanguageCodesEnum.CHINESE_SIMPLIFIED,
    DLE.CZECH: AWSTranslationLanguageCodesEnum.CZECH,
    DLE.DANISH: AWSTranslationLanguageCodesEnum.DANISH,
    DLE.DUTCH: AWSTranslationLanguageCodesEnum.DUTCH,
    DLE.ENGLISH: AWSTranslationLanguageCodesEnum.ENGLISH,
    DLE.ENGLISH_AUSTRALIA: AWSTranslationLanguageCodesEnum.ENGLISH,
    DLE.ENGLISH_INDIA: AWSTranslationLanguageCodesEnum.ENGLISH,
    DLE.ENGLISH_NEW_ZEALAND: AWSTranslationLanguageCodesEnum.ENGLISH,
    DLE.ENGLISH_UNITED_KINGDOM: AWSTranslationLanguageCodesEnum.ENGLISH,
    DLE.ENGLISH_UNITED_STATES: AWSTranslationLanguageCodesEnum.ENGLISH,
    DLE.ESTONIAN: AWSTranslationLanguageCodesEnum.ESTONIAN,
    DLE.FINNISH: AWSTranslationLanguageCodesEnum.FINNISH,
    DLE.FLEMISH: AWSTranslationLanguageCodesEnum.DUTCH,
    DLE.FRENCH: AWSTranslationLanguageCodesEnum.FRENCH,
    DLE.FRENCH_CANADA: AWSTranslationLanguageCodesEnum.FRENCH_CANADA,
    DLE.GERMAN: AWSTranslationLanguageCodesEnum.GERMAN,
    DLE.GREEK: AWSTranslationLanguageCodesEnum.GREEK,
    DLE.HINDI: AWSTranslationLanguageCodesEnum.HINDI,
    DLE.HUNGARIAN: AWSTranslationLanguageCodesEnum.HUNGARIAN,
    DLE.INDONESIAN: AWSTranslationLanguageCodesEnum.INDONESIAN,
    DLE.ITALIAN: AWSTranslationLanguageCodesEnum.ITALIAN,
    DLE.JAPANESE: AWSTranslationLanguageCodesEnum.JAPANESE,
    DLE.KOREAN: AWSTranslationLanguageCodesEnum.KOREAN,
    DLE.LATVIAN: AWSTranslationLanguageCodesEnum.LATVIAN,
    DLE.LITHUANIAN: AWSTranslationLanguageCodesEnum.LITHUANIAN,
    DLE.MALAY: AWSTranslationLanguageCodesEnum.MALAY,
    DLE.NORWEGIAN: AWSTranslationLanguageCodesEnum.NORWEGIAN,
    DLE.POLISH: AWSTranslationLanguageCodesEnum.POLISH,
    DLE.PORTUGUESE: AWSTranslationLanguageCodesEnum.PORTUGUESE,
    DLE.PORTUGUESE_BRAZIL: AWSTranslationLanguageCodesEnum.PORTUGUESE,
    DLE.PORTUGUESE_PORTUGAL: AWSTranslationLanguageCodesEnum.PORTUGUESE_PORTUGAL,
    DLE.ROMANIAN: AWSTranslationLanguageCodesEnum.ROMANIAN,
    DLE.RUSSIAN: AWSTranslationLanguageCodesEnum.RUSSIAN,
    DLE.SLOVAK: AWSTranslationLanguageCodesEnum.SLOVAK,
    DLE.SPANISH: AWSTranslationLanguageCodesEnum.SPANISH,
    DLE.SPANISH_LATIN_AMERICA: AWSTranslationLanguageCodesEnum.SPANISH,
    DLE.SWEDISH: AWSTranslationLanguageCodesEnum.SWEDISH,
    DLE.SWISS_GERMAN: AWSTranslationLanguageCodesEnum.GERMAN,
    DLE.THAI: AWSTranslationLanguageCodesEnum.THAI,
    DLE.TURKISH: AWSTranslationLanguageCodesEnum.TURKISH,
    DLE.UKRAINIAN: AWSTranslationLanguageCodesEnum.UKRAINIAN,
    DLE.VIETNAMESE: AWSTranslationLanguageCodesEnum.VIETNAMESE,
}

DEEPGRAM_LANGUAGE_CODE_TO_SCHEMA_LANGUAGE_ENUM = {
    DLE.BULGARIAN: LE.BG,
    DLE.CATALAN: LE.CA,
    DLE.CHINESE: LE.ZH_CH,
    DLE.CHINESE_CHINA: LE.ZH_CH,
    DLE.CHINESE_TAIWAN: LE.ZH_CH,
    DLE.CZECH: LE.CS,
    DLE.DANISH: LE.DA_DK,
    DLE.DUTCH: LE.NL_NL,
    DLE.ENGLISH: LE.EN_GB,
    DLE.ENGLISH_AUSTRALIA: LE.EN_AU,
    DLE.ENGLISH_INDIA: LE.EN_IN,
    DLE.ENGLISH_NEW_ZEALAND: LE.EN_NZ,
    DLE.ENGLISH_UNITED_KINGDOM: LE.EN_GB,
    DLE.ENGLISH_UNITED_STATES: LE.EN_US,
    DLE.ESTONIAN: LE.ET,
    DLE.FINNISH: LE.FI,
    DLE.FLEMISH: LE.NL_NL,
    DLE.FRENCH: LE.FR_FR,
    DLE.FRENCH_CANADA: LE.FR_CA,
    DLE.GERMAN: LE.DE_DE,
    DLE.GREEK: LE.EL,
    DLE.HINDI: LE.HI_IN,
    DLE.HUNGARIAN: LE.HU,
    DLE.INDONESIAN: LE.ID_ID,
    DLE.ITALIAN: LE.IT_IT,
    DLE.JAPANESE: LE.JA_JP,
    DLE.KOREAN: LE.KO_KR,
    DLE.LATVIAN: LE.LV,
    DLE.LITHUANIAN: LE.LT,
    DLE.MALAY: LE.MS_MY,
    DLE.NORWEGIAN: LE.NO,
    DLE.PORTUGUESE: LE.PT_PT,
    DLE.PORTUGUESE_BRAZIL: LE.PT_BR,
    DLE.PORTUGUESE_PORTUGAL: LE.PT_PT,
    DLE.ROMANIAN: LE.RO,
    DLE.RUSSIAN: LE.RU_RU,
    DLE.SLOVAK: LE.SK,
    DLE.SPANISH: LE.ES_ES,
    DLE.SPANISH_LATIN_AMERICA: LE.ES_ES,
    DLE.SWEDISH: LE.SV_SE,
    DLE.SWISS_GERMAN: LE.DE_CH,
    DLE.THAI: LE.TH,
    DLE.TURKISH: LE.TR_TR,
    DLE.UKRAINIAN: LE.UK,
    DLE.VIETNAMESE: LE.VI,
}

TRANSCRIPTION_USAGE_MAP = {
    VendorEnum.AWS.value: 0,
    VendorEnum.DEEP_GRAM.value: 0,
    VendorEnum.FANO.value: 0,
    VendorEnum.INTELLIGENT_VOICE.value: 0,
    VendorEnum.SPEECHMATICS.value: 0,
    VendorEnum.VERINT.value: 0,
}

COUNTRY_TO_LANGUAGE_ENUM_MAP = {
    "brazil": LE.PT_BR,
    "brasil": LE.PT_BR,
    "bra": LE.PT_BR,
    "br": LE.PT_BR,
    "portugal": LE.PT_PT,
    "pt": LE.PT_PT,
    "angola": LE.PT_PT,
    "spain": LE.ES_ES,
    "es": LE.ES_ES,
    "mexico": LE.ES_ES,
    "colombia": LE.ES_ES,
    "argentina": LE.ES_ES,
    "peru": LE.ES_ES,
    "venezuela": LE.ES_ES,
    "chile": LE.ES_ES,
    "guatemala": LE.ES_ES,
    "ecuador": LE.ES_ES,
    "bolivia": LE.ES_ES,
    "cuba": LE.ES_ES,
    "dominican republic": LE.ES_ES,
    "republica dominicana": LE.ES_ES,
    "honduras": LE.ES_ES,
    "paraguay": LE.ES_ES,
    "paraguai": LE.ES_ES,
    "el salvador": LE.ES_ES,
    "nicaragua": LE.ES_ES,
    "costa rica": LE.ES_ES,
    "panama": LE.ES_ES,
    "uruguay": LE.ES_ES,
    "uruguai": LE.ES_ES,
    "equatorial guinea": LE.ES_ES,
    "ru": LE.RU_RU,
    "russia": LE.RU_RU,
    "ja": LE.JA_JP,
    "jp": LE.JA_JP,
    "japan": LE.JA_JP,
    "de": LE.DE_DE,
    "deutschland": LE.DE_DE,
    "germany": LE.DE_DE,
    "zh": LE.ZH_CN,
    "china": LE.ZH_CN,
    "nl": LE.NL_NL,
    "netherlands": LE.NL_NL,
    "fr": LE.FR_FR,
    "france": LE.FR_FR,
}

LANGUAGE_CODE_TO_AMAZON_LANGUAGE_CODE = {
    LanguageCodeEnum.ARABIC: AWSTranslationLanguageCodesEnum.ARABIC,
    LanguageCodeEnum.CATALAN: AWSTranslationLanguageCodesEnum.CATALAN,
    LanguageCodeEnum.CHINESE: AWSTranslationLanguageCodesEnum.CHINESE_SIMPLIFIED,
    LanguageCodeEnum.CROATIAN: AWSTranslationLanguageCodesEnum.CROATIAN,
    LanguageCodeEnum.DANISH: AWSTranslationLanguageCodesEnum.DANISH,
    LanguageCodeEnum.DUTCH: AWSTranslationLanguageCodesEnum.DUTCH,
    LanguageCodeEnum.ENGLISH: AWSTranslationLanguageCodesEnum.ENGLISH,
    LanguageCodeEnum.FRENCH: AWSTranslationLanguageCodesEnum.FRENCH,
    LanguageCodeEnum.GERMAN: AWSTranslationLanguageCodesEnum.GERMAN,
    LanguageCodeEnum.GREEK: AWSTranslationLanguageCodesEnum.GREEK,
    LanguageCodeEnum.HEBREW: AWSTranslationLanguageCodesEnum.HEBREW,
    LanguageCodeEnum.HINDI: AWSTranslationLanguageCodesEnum.HINDI,
    LanguageCodeEnum.INDONESIAN: AWSTranslationLanguageCodesEnum.INDONESIAN,
    LanguageCodeEnum.ITALIAN: AWSTranslationLanguageCodesEnum.ITALIAN,
    LanguageCodeEnum.JAPANESE: AWSTranslationLanguageCodesEnum.JAPANESE,
    LanguageCodeEnum.KOREAN: AWSTranslationLanguageCodesEnum.KOREAN,
    LanguageCodeEnum.NORWEGIAN: AWSTranslationLanguageCodesEnum.NORWEGIAN,
    LanguageCodeEnum.POLISH: AWSTranslationLanguageCodesEnum.POLISH,
    LanguageCodeEnum.PORTUGUESE: AWSTranslationLanguageCodesEnum.PORTUGUESE,
    LanguageCodeEnum.RUSSIAN: AWSTranslationLanguageCodesEnum.RUSSIAN,
    LanguageCodeEnum.SPANISH: AWSTranslationLanguageCodesEnum.SPANISH,
    LanguageCodeEnum.SWEDISH: AWSTranslationLanguageCodesEnum.SWEDISH,
    LanguageCodeEnum.TAGALOG: AWSTranslationLanguageCodesEnum.FILIPINO_TAGALOG,
    LanguageCodeEnum.THAI: AWSTranslationLanguageCodesEnum.THAI,
    LanguageCodeEnum.TURKISH: AWSTranslationLanguageCodesEnum.TURKISH,
}

LANGUAGE_CODE_TO_DEEPGRAM_LANGUAGE_CODE = {
    LanguageCodeEnum.BULGARIAN: DeepGramLanguageEnum.BULGARIAN,
    LanguageCodeEnum.CATALAN: DeepGramLanguageEnum.CATALAN,
    LanguageCodeEnum.CHINESE: DeepGramLanguageEnum.CHINESE,
    LanguageCodeEnum.CZECH: DeepGramLanguageEnum.CZECH,
    LanguageCodeEnum.DANISH: DeepGramLanguageEnum.DANISH,
    LanguageCodeEnum.DUTCH: DeepGramLanguageEnum.DUTCH,
    LanguageCodeEnum.ENGLISH: DeepGramLanguageEnum.ENGLISH,
    LanguageCodeEnum.ESTONIAN: DeepGramLanguageEnum.ESTONIAN,
    LanguageCodeEnum.FINNISH: DeepGramLanguageEnum.FINNISH,
    LanguageCodeEnum.FRENCH: DeepGramLanguageEnum.FRENCH,
    LanguageCodeEnum.GERMAN: DeepGramLanguageEnum.GERMAN,
    LanguageCodeEnum.GREEK: DeepGramLanguageEnum.GREEK,
    LanguageCodeEnum.HINDI: DeepGramLanguageEnum.HINDI,
    LanguageCodeEnum.HUNGARIAN: DeepGramLanguageEnum.HUNGARIAN,
    LanguageCodeEnum.INDONESIAN: DeepGramLanguageEnum.INDONESIAN,
    LanguageCodeEnum.ITALIAN: DeepGramLanguageEnum.ITALIAN,
    LanguageCodeEnum.JAPANESE: DeepGramLanguageEnum.JAPANESE,
    LanguageCodeEnum.KOREAN: DeepGramLanguageEnum.KOREAN,
    LanguageCodeEnum.LATVIAN: DeepGramLanguageEnum.LATVIAN,
    LanguageCodeEnum.LITHUANIAN: DeepGramLanguageEnum.LITHUANIAN,
    # LanguageCodeEnum.MALAY: DeepGramLanguageEnum.MALAY,
    # LanguageCodeEnum.MANDARIN: DeepGramLanguageEnum.CHINESE,
    LanguageCodeEnum.NORWEGIAN: DeepGramLanguageEnum.NORWEGIAN,
    LanguageCodeEnum.POLISH: DeepGramLanguageEnum.POLISH,
    LanguageCodeEnum.PORTUGUESE: DeepGramLanguageEnum.PORTUGUESE,
    LanguageCodeEnum.ROMANIAN: DeepGramLanguageEnum.ROMANIAN,
    LanguageCodeEnum.RUSSIAN: DeepGramLanguageEnum.RUSSIAN,
    LanguageCodeEnum.SLOVAK: DeepGramLanguageEnum.SLOVAK,
    LanguageCodeEnum.SPANISH: DeepGramLanguageEnum.SPANISH,
    LanguageCodeEnum.SWEDISH: DeepGramLanguageEnum.SWEDISH,
    LanguageCodeEnum.SWISS_GERMAN: DeepGramLanguageEnum.SWISS_GERMAN,
    LanguageCodeEnum.THAI: DeepGramLanguageEnum.THAI,
    LanguageCodeEnum.TURKISH: DeepGramLanguageEnum.TURKISH,
    LanguageCodeEnum.UKRAINIAN: DeepGramLanguageEnum.UKRAINIAN,
    LanguageCodeEnum.VIETNAMESE: DeepGramLanguageEnum.VIETNAMESE,
}

LANGUAGE_CODE_TO_AZURE_LANGUAGE_CODE = {
    LanguageCodeEnum.ARABIC: "ar",
    LanguageCodeEnum.CATALAN: "ca",
    LanguageCodeEnum.CHINESE: "zh-Hans",
    LanguageCodeEnum.CROATIAN: "hr",
    LanguageCodeEnum.DANISH: "da",
    LanguageCodeEnum.DUTCH: "nl",
    LanguageCodeEnum.ENGLISH: "en",
    LanguageCodeEnum.FRENCH: "fr",
    LanguageCodeEnum.GERMAN: "de",
    LanguageCodeEnum.GREEK: "el",
    LanguageCodeEnum.HEBREW: "he",
    LanguageCodeEnum.HINDI: "hi",
    LanguageCodeEnum.INDONESIAN: "id",
    LanguageCodeEnum.ITALIAN: "it",
    LanguageCodeEnum.JAPANESE: "ja",
    LanguageCodeEnum.KOREAN: "ko",
    LanguageCodeEnum.NORWEGIAN: "nb",
    LanguageCodeEnum.POLISH: "pl",
    LanguageCodeEnum.PORTUGUESE: "pt",
    LanguageCodeEnum.RUSSIAN: "ru",
    LanguageCodeEnum.SPANISH: "es",
    LanguageCodeEnum.SWEDISH: "sv",
    LanguageCodeEnum.TAGALOG: "fil",
    LanguageCodeEnum.THAI: "th",
    LanguageCodeEnum.TURKISH: "tr",
}


class TranscriptionApiDerivedColumns:
    CALL_DURATION_IN_SECONDS = "call_duration_in_seconds"
    DURATION_IN_RANGE_FLAG = "duration_in_range_flag"


TRANSCRIPTION_FEED_FLOW_NAME = "transcription_feed"


TRANSCRIPTION_FEED_SOURCE_SCHEMA = {
    "&id": "string",
    "&model": "string",
    "&hash": "string",
    TranscriberTargetFields.DATE_TIME_TRANSCRIPTION_END: "string",
    TranscriberTargetFields.DATE_TIME_TRANSCRIPTION_START: "string",
    TranscriberTargetFields.JOB_ID: "string",
    TranscriberTargetFields.MODEL_ID: "string",
    TranscriberTargetFields.RECORDING_SOURCE_KEY: "string",
    TranscriberTargetFields.SOURCE_AUDIO_LANGUAGE: "string",
    TranscriberTargetFields.TARGET_LANGUAGE: "string",
    TranscriberTargetFields.TRANSCRIPTION_CONFIDENCE: "float",
    TranscriberTargetFields.TRANSCRIPTION_PROVIDER: "string",
    TranscriberTargetFields.TRANSCRIPTION_STATUS: "string",
    TranscriberTargetFields.TRANSCRIPTION_STATUS_DESCRIPTION: "string",
    TranscriberTargetFields.TRANSCRIPT_SOURCE_KEY: "string",
    TranscriberTargetFields.TRANSLATION_PROVIDER: "string",
}
