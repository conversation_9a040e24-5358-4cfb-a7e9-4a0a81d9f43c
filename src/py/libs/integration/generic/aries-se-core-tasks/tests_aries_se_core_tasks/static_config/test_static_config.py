import httpx
import pytest
from aries_se_api_client.base import ParsedResponse
from mock.mock import patch
from pydantic import BaseModel, Field


class TenantTestModel(BaseModel):
    feature_a: bool = Field(default=False, description="Feature A")
    some_setting: str = Field(default="default", description="Setting")


class TenantWorkflowTestModel(BaseModel):
    threshold: float = Field(default=0.5, description="Threshold")
    mode: str = Field(default="balanced", description="Mode")


@pytest.fixture(autouse=True)
def mock_env_vars(monkeypatch):
    """
    Fixture to automatically mock environment variables for all tests in this file.
    """
    monkeypatch.setenv("DATA_PLATFORM_CONFIG_API_URL", "http://fake-api-from-env.com")
    monkeypatch.setenv("STACK", "test-stack")


@patch("aries_se_core_tasks.static_config.static_config.TenantWorkflowAPI")
@patch("aries_se_core_tasks.static_config.static_config.TenantAPI")
class TestGetStaticConfig:
    def test_get_tenant_config_success(self, mock_tenant_client, mock_tenant_workflow_client):
        """Verify successful parsing of tenant static_config."""
        from aries_se_core_tasks.static_config.static_config import (
            ConfigApiTable,
            get_static_config,
        )

        mock_response = ParsedResponse(
            content={"static_config": {"feature_a": True, "some_setting": "custom"}},
            raw_response=httpx.Response(status_code=200),
        )
        mock_tenant_client.return_value.get.return_value = mock_response

        config = get_static_config(
            table=ConfigApiTable.TENANT,
            model=TenantTestModel,
            tenant_name="test-tenant",
            stack_name="test-stack",
        )

        assert isinstance(config, TenantTestModel)
        assert config.feature_a is True
        assert config.some_setting == "custom"
        mock_tenant_client.return_value.get.assert_called_once()
        mock_tenant_workflow_client.get.assert_not_called()

    def test_get_tenant_config_with_defaults(self, mock_tenant_client, mock_tenant_workflow_client):
        """Verify we get default values when tenant static_config is missing."""
        from aries_se_core_tasks.static_config.static_config import (
            ConfigApiTable,
            get_static_config,
        )

        mock_tenant_client.get.return_value = {"dummy_key": "dummy_value"}

        config = get_static_config(
            table=ConfigApiTable.TENANT,
            model=TenantTestModel,
            tenant_name="test-tenant",
            stack_name="test-stack",
        )

        assert isinstance(config, TenantTestModel)
        assert config.feature_a is False
        assert config.some_setting == "default"

    def test_get_workflow_config_success(self, mock_tenant_client, mock_tenant_workflow_client):
        """Verify successful parsing of tenant_workflow static_config."""
        from aries_se_core_tasks.static_config.static_config import (
            ConfigApiTable,
            get_static_config,
        )

        mock_response = ParsedResponse(
            content={"static_config": {"threshold": 0.99, "mode": "strict"}},
            raw_response=httpx.Response(status_code=200),
        )
        mock_tenant_workflow_client.return_value.get.return_value = mock_response

        config = get_static_config(
            table=ConfigApiTable.TENANT_WORKFLOW,
            model=TenantWorkflowTestModel,
            tenant_name="test-tenant",
            workflow_name="test-workflow",
            stack_name="test-stack",
        )

        # Assert
        assert isinstance(config, TenantWorkflowTestModel)
        assert config.threshold == 0.99
        assert config.mode == "strict"
        mock_tenant_workflow_client.return_value.get.assert_called_once()
        mock_tenant_client.get.assert_not_called()

    def test_get_workflow_config_with_defaults(
        self, mock_tenant_client, mock_tenant_workflow_client
    ):
        """Verify fallback to defaults for workflow static_config."""
        from aries_se_core_tasks.static_config.static_config import (
            ConfigApiTable,
            get_static_config,
        )

        # Mock: No static config returned
        mock_tenant_workflow_client.get.return_value = {}

        config = get_static_config(
            table=ConfigApiTable.TENANT_WORKFLOW,
            model=TenantWorkflowTestModel,
            tenant_name="test-tenant",
            workflow_name="test-workflow",
            stack_name="test-stack",
        )

        assert isinstance(config, TenantWorkflowTestModel)
        assert config.threshold == 0.5  # Default
        assert config.mode == "balanced"  # Default

    def test_missing_workflow_name_raises_error(
        self, mock_tenant_client, mock_tenant_workflow_client
    ):
        """Verify ValueError if workflow_name is missing for tenant_workflow table."""
        from aries_se_core_tasks.static_config.static_config import (
            ConfigApiTable,
            get_static_config,
        )

        with pytest.raises(
            ValueError,
            match="`workflow_name` is required when table='tenant_workflow'",
        ):
            get_static_config(
                table=ConfigApiTable.TENANT_WORKFLOW,
                model=TenantWorkflowTestModel,
                tenant_name="test-tenant",
                stack_name="test-stack",
            )

    def test_missing_api_host_raises_error(
        self, mock_tenant_client, mock_tenant_workflow_client, monkeypatch
    ):
        """Verify ValueError if API host is not configured."""
        from aries_se_core_tasks.static_config.static_config import (
            ConfigApiTable,
            get_static_config,
        )

        # Remove the env var for this test only
        monkeypatch.delenv("DATA_PLATFORM_CONFIG_API_URL")
        with pytest.raises(
            ValueError,
            match="DATA_PLATFORM_CONFIG_API_URL is not set and no `api_host` was provided.",
        ):
            get_static_config(
                table=ConfigApiTable.TENANT,
                model=TenantTestModel,
                tenant_name="test-tenant",
                stack_name="test-stack",
            )

    @patch("aries_se_core_tasks.static_config.static_config.AriesApiClient")
    def test_api_host_parameter_is_used(
        self, mock_aries_client, mock_tenant_client, mock_tenant_workflow_client
    ):
        """Verify the api_host parameter overrides the environment variable."""
        from aries_se_core_tasks.static_config.static_config import (
            ConfigApiTable,
            get_static_config,
        )

        host_override = "http://specific-host.com"
        mock_tenant_client.get.return_value = {}

        get_static_config(
            table=ConfigApiTable.TENANT,
            model=TenantTestModel,
            tenant_name="test-tenant",
            stack_name="test-stack",
            api_host=host_override,
        )

        mock_aries_client.assert_called_once_with(host=host_override)
