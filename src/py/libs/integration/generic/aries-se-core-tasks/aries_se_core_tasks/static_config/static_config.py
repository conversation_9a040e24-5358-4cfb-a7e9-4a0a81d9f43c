import os
from aries_se_api_client.client import AriesApiClient
from data_platform_config_api_client.tenant import <PERSON>antAP<PERSON>
from data_platform_config_api_client.tenant_workflow import TenantWorkflowAP<PERSON>
from enum import StrEnum
from pydantic import BaseModel
from typing import Optional, Type, TypeVar

# Type variable for any subclass of BaseModel.
TModel = TypeVar("TModel", bound=BaseModel)


class ConfigApiTable(StrEnum):
    TENANT = "tenant"
    TENANT_WORKFLOW = "tenant_workflow"


def get_static_config(
    table: ConfigApiTable,
    model: Type[TModel],
    tenant_name: str,
    stack_name: str,
    workflow_name: Optional[str] = None,
    api_host: Optional[str] = None,
) -> TModel:
    """
    Fetch the `static_config` column from either the Tenant or TenantWorkflow table
    using the respective APIs, and parse it into the provided Pydantic model.

    The Pydantic class should be passed (not the instance), and the
    function will return an instance of that model populated from the `static_config`
    column if present, or with default values provided by the model if not (if
    default values are configured in the model).


    :param table : Table from which we fetch `static_config`.
    :param model : A Pydantic model class (subclass of `BaseModel`) to parse the
     `static_config` into.
    :param tenant_name : Tenant identifier (required for both tables).
    :param stack_name: Stack name (required for both tables).
    :param workflow_name : Workflow identifier. Required when `table="tenant_workflow"`.
    :param api_host : Override the host for `AriesApiClient`. Defaults to env var
        `DATA_PLATFORM_CONFIG_API_URL` if not provided.

    :returns An instance of `model`, populated from the `static_config` column when present,
        or constructed with the model's defaults if not.

    :raises ValueError
        - If the API host cannot be determined.
        - If `workflow_name` is missing when `level="tenant_workflow"`.


    Examples:

    Fetch tenant-level static_config into a pydantic model.

    >>> from pydantic import BaseModel
    >>> class TenantLevelCfg(BaseModel):
    ...     enable_language_identification_voice: bool = False
    >>> cfg = get_static_config(
    ...     table=ConfigApiTable.TENANT,
    ...     model=TenantLevelCfg,
    ...     tenant_name="test",
    ... )

    Fetch tenant-workflow-level static_config into a pydantic model.

    >>> class TenantWorkflowLevelCfg(BaseModel):
    ...     threshold: float = 0.7
    >>> tw_cfg = get_static_config(
    ...     table=ConfigApiTable.TENANT_WORKFLOW,
    ...     model=TenantWorkflowLevelCfg,
    ...     tenant_name="test",
    ...     workflow_name="dummy",
    ... )

    """
    host = api_host or os.getenv("DATA_PLATFORM_CONFIG_API_URL")
    if not host:
        raise ValueError("DATA_PLATFORM_CONFIG_API_URL is not set and no `api_host` was provided.")

    client = AriesApiClient(host=host)

    if table == ConfigApiTable.TENANT:
        tenant_instance = TenantAPI(client=client)
        response = tenant_instance.get(
            stack_name=stack_name,
            tenant_name=tenant_name,
        )
    elif table == ConfigApiTable.TENANT_WORKFLOW:
        if not workflow_name:
            raise ValueError("`workflow_name` is required when table='tenant_workflow'")
        tenant_workflow_instance = TenantWorkflowAPI(client=client)
        response = tenant_workflow_instance.get(
            stack_name=stack_name,
            tenant_name=tenant_name,
            workflow_name=workflow_name,
        )
    else:
        raise ValueError("get_static_config only works for tenant and tenant_workflow")

    static_config_value = response.content.get("static_config") or dict()
    return model(**static_config_value)
