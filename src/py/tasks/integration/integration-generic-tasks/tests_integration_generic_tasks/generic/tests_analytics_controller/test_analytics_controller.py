import httpx
import pytest
from aries_se_api_client.base import ParsedR<PERSON>ponse
from aries_se_core_tasks.io.read import fetch_tenant_configuration
from aries_task_link.models import AriesTaskInput
from integration_generic_tasks.generic.analytics_controller.analytics_controller_task import (
    analytics_controller_run,
)
from mock.mock import patch
from se_elasticsearch.repository import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix


class TestAnalyticsController:
    @pytest.mark.parametrize(
        "status_code, expected_lexica_result, tenant_config, "
        "expected_language_detection_result, expected_transcription_result",
        [
            (
                200,
                True,
                {
                    "notFeatureFlags": ["language-detection", "dummy"],
                    "voice": {"transcriptionProvider": "ABC"},
                    "contractualLimits": {"volume": {"voice": {"transcription": 123}}},
                },
                False,
                {"transcription_provider": "ABC", "transcription_limit": 123},
            ),
            (
                404,
                False,
                {"featureFlags": ["language-detection", "dummy"]},
                True,
                {"transcription_provider": None, "transcription_limit": None},
            ),
        ],
    )
    def test_end_to_end(
        self,
        mocker,
        status_code,
        expected_lexica_result,
        tenant_config,
        expected_language_detection_result,
        expected_transcription_result,
        sample_aries_task_input: AriesTaskInput,
    ):
        mock_lexica_api_request(mocker=mocker, status_code=status_code)

        result = self._run_aries_task(  # type: ignore
            sample_aries_task_input=sample_aries_task_input,
            tenant_config=tenant_config,
            mocker=mocker,
        )

        assert (
            result.app_metric.metrics["dit"]["email"]["analytics_controller"][
                "flow_execution_time_ms"
            ]
            > 0
        )
        assert result.output_param.params["is_lexica_enabled"] == expected_lexica_result
        assert (
            result.output_param.params["is_language_detection_enabled"]
            == expected_language_detection_result
        )
        assert result.output_param.params[
            "transcription_provider"
        ] == expected_transcription_result.get("transcription_provider")
        assert result.output_param.params[
            "transcription_limit"
        ] == expected_transcription_result.get("transcription_limit")
        assert result.output_param.params["tenant_config_feature_flags"] == tenant_config.get(
            "featureFlags", []
        )
        assert result.output_param.params["is_language_identification_voice_enabled"] is False

    @pytest.mark.parametrize(
        "status_code, expected_lexica_result, tenant_config, "
        "expected_language_detection_result, expected_transcription_result",
        [
            (
                200,
                False,
                {
                    "notFeatureFlags": ["language-detection", "dummy"],
                    "voice": {"transcriptionProvider": "ABC"},
                    "contractualLimits": {"volume": {"voice": {"transcription": 123}}},
                },
                False,
                {"transcription_provider": "ABC", "transcription_limit": 123},
            ),
            (
                404,
                False,
                {"featureFlags": ["language-detection", "dummy"]},
                True,
                {"transcription_provider": None, "transcription_limit": None},
            ),
        ],
    )
    def test_skip_lexica_file_uri(
        self,
        mocker,
        status_code,
        expected_lexica_result,
        tenant_config,
        expected_language_detection_result,
        expected_transcription_result,
        sample_aries_task_input_skip_lexica: AriesTaskInput,
    ):
        result = self._run_aries_task(
            sample_aries_task_input=sample_aries_task_input_skip_lexica,
            tenant_config=tenant_config,
            mocker=mocker,
        )

        assert (
            result.app_metric.metrics["dit"]["email"]["analytics_controller"][
                "flow_execution_time_ms"
            ]
            > 0
        )
        assert result.output_param.params["is_lexica_enabled"] == expected_lexica_result
        assert (
            result.output_param.params["is_language_detection_enabled"]
            == expected_language_detection_result
        )
        assert result.output_param.params[
            "transcription_provider"
        ] == expected_transcription_result.get("transcription_provider")
        assert result.output_param.params[
            "transcription_limit"
        ] == expected_transcription_result.get("transcription_limit")
        assert result.output_param.params["tenant_config_feature_flags"] == tenant_config.get(
            "featureFlags", []
        )

    @pytest.mark.parametrize(
        "static_config_tenant, static_config_tenant_workflow, expected_enabled",
        [
            #  Neither tenant not tenant_workflow static_config present
            pytest.param(None, None, False, id="Both_none->False"),
            #  Only tenant static_config present
            pytest.param(
                {"enable_language_identification_voice": True},
                None,
                True,
                id="tenantTrue_only->True",
            ),
            pytest.param(
                {"enable_language_identification_voice": False},
                None,
                False,
                id="tenantFalse_only->False",
            ),
            #  Only tenant_workflow static_config present
            pytest.param(
                None, {"enable_language_identification_voice": True}, True, id="twTrue_only->True"
            ),
            pytest.param(
                None,
                {"enable_language_identification_voice": False},
                False,
                id="twFalse_only->False",
            ),
            # Both tenant and tenant_workflow static_config present - We have 4 possible
            # combinations of True and False
            pytest.param(
                {"enable_language_identification_voice": True},
                {"enable_language_identification_voice": True},
                True,
                id="tenantTrue_twTrue->True",
            ),
            pytest.param(
                {"enable_language_identification_voice": True},
                {"enable_language_identification_voice": False},
                False,
                id="tenantTrue_twFalse->False",
            ),
            pytest.param(
                {"enable_language_identification_voice": False},
                {"enable_language_identification_voice": True},
                True,
                id="tenantFalse_twTrue->True",
            ),
            pytest.param(
                {"enable_language_identification_voice": False},
                {"enable_language_identification_voice": False},
                False,
                id="tenantFalse_twFalse->False",
            ),
        ],
    )
    def test_language_identification_voice_enabled_precedence_all_cases(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        static_config_tenant,
        static_config_tenant_workflow,
        expected_enabled,
    ):
        mock_lexica_api_request(mocker=mocker, status_code=404)

        tenant_config = {}

        result = self._run_aries_task(
            sample_aries_task_input=sample_aries_task_input,
            tenant_config=tenant_config,
            mocker=mocker,
            static_config_tenant=static_config_tenant,
            static_config_tenant_workflow=static_config_tenant_workflow,
        )

        assert (
            result.output_param.params["is_language_identification_voice_enabled"]
            == expected_enabled
        )

    @staticmethod
    @patch.object(fetch_tenant_configuration, "get_repository_by_cluster_version")
    @patch.object(fetch_tenant_configuration, "get_es_config")
    def _run_aries_task(
        mock_get_es_config,
        mock_get_repository_by_cluster_version,
        tenant_config: dict,
        sample_aries_task_input: AriesTaskInput,
        mocker,
        static_config_tenant: dict | None = None,
        static_config_tenant_workflow: dict | None = None,
    ):
        # Mocks for elastic config
        mock_get_es_config.return_value = ResourceConfig(
            host="localhost",
            port=9200,
            scheme="http",
            meta_prefix=MetaPrefix.AMPERSAND,
        )
        mock_tenant_and_tenant_workflow_config_db(
            mocker=mocker,
            static_config_tenant=static_config_tenant,
            static_config_tenant_workflow=static_config_tenant_workflow,
        )

        # Mocks elastic repository
        es_obj = mock_get_repository_by_cluster_version.return_value
        es_obj.search.return_value = {"hits": {"hits": [{"_source": tenant_config}]}}
        es_obj.meta.prefix = MetaPrefix.AMPERSAND
        es_obj.meta.key = "&key"
        es_obj.meta.id = "&id"

        return analytics_controller_run(aries_task_input=sample_aries_task_input)


def mock_lexica_api_request(mocker, status_code: int):
    """helper function to mock the api request in GetTargetEventAction since
    it's used in almost every flow test.

    :param mocker: mocker
    :param status_code: status code
    """
    mock_lexica_api = mocker.patch(
        "workflow_controllers.check_lexica_endpoint.CachedLexicaAPIClient"
    )
    if status_code == 200:
        mock_lexica_api.get_se_lexica_version.return_value = {"se_lexica_version": "v7"}
    else:
        mock_lexica_api.get_se_lexica_version.return_value = {}


def mock_tenant_and_tenant_workflow_config_db(
    mocker,
    static_config_tenant: dict | None = None,
    static_config_tenant_workflow: dict | None = None,
):
    """
    Func to mock fetching of tenant and tenant workflow config from
    config db

    :param mocker: mocker
    :param static_config_tenant: Static config for tenant
    :param static_config_tenant_workflow: Static config for tenant workflow
    """
    mock_tenant_client = mocker.patch("aries_se_core_tasks.static_config.static_config.TenantAPI")
    mock_tenant_workflow_client = mocker.patch(
        "aries_se_core_tasks.static_config.static_config.TenantWorkflowAPI"
    )
    tenant_result = (
        ParsedResponse(content={"dummy": "dummy"}, raw_response=httpx.Response(status_code=200))
        if static_config_tenant is None
        else ParsedResponse(
            content={"static_config": static_config_tenant},
            raw_response=httpx.Response(status_code=200),
        )
    )
    tenant_workflow_result = (
        ParsedResponse(content={}, raw_response=httpx.Response(status_code=200))
        if static_config_tenant_workflow is None
        else ParsedResponse(
            content={"static_config": static_config_tenant_workflow},
            raw_response=httpx.Response(status_code=200),
        )
    )

    mock_tenant_client.return_value.get.return_value = tenant_result
    mock_tenant_workflow_client.return_value.get.return_value = tenant_workflow_result
