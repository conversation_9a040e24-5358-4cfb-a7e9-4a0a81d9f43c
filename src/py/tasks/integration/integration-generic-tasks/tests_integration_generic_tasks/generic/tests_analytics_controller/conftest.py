import os
import pytest
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables

os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["STACK"] = "dev-shared-2"


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="email",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/lake/ingress/090033___rfCzWyUS4gh08Ikt2HZ0M___batch.ndjson",  # noqa: E501
        )
    )

    task = TaskFieldSet(name="analytics_controller", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def sample_aries_task_input_skip_lexica() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        name="email",
        stack="dev-blue",
        tenant="test",
        start_timestamp=datetime(2022, 7, 6),
        trace_id="trace",
    )

    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://test.dev.steeleye.co/aries/ingress/nonstreamed/evented/"
            "voice_poll/2025/06/02/trace_id_skip_lexica/file.json",  # noqa: E501
        )
    )

    task = TaskFieldSet(name="analytics_controller", version="latest", success=False)

    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
