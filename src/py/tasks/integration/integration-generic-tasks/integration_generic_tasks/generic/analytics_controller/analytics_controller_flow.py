import logging
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.io.read.fetch_tenant_configuration import run_fetch_tenant_configuration
from aries_se_core_tasks.static_config.static_config import ConfigApiTable, get_static_config
from aries_task_link.models import AriesTaskInput
from integration_generic_tasks.generic.analytics_controller.input_schema import (
    AnalyticsControllerTenantStaticConfig,
    AnalyticsControllerTenantWorkflowStaticConfig,
)
from workflow_controllers.check_language_detection import check_language_detection
from workflow_controllers.check_lexica_endpoint import is_lexica_enabled_for_tenant

logger = logging.getLogger(__name__)


def analytics_controller_flow(aries_task_input: AriesTaskInput, result_path: str = "result.json"):
    tenant = aries_task_input.workflow.tenant
    workflow = aries_task_input.workflow.name
    stack = aries_task_input.workflow.stack

    file_uri = aries_task_input.input_param.params.get("file_uri", "")

    # Determine if the tenant has lexica pre-processing enabled
    is_lexica_enabled = is_lexica_enabled_for_tenant(tenant, stack, file_uri)

    tenant_configuration = run_fetch_tenant_configuration(
        tenant=tenant,
    )
    # Fetch tenant-level static config
    tenant_static_config = get_static_config(
        table=ConfigApiTable.TENANT,
        model=AnalyticsControllerTenantStaticConfig,
        tenant_name=tenant,
        stack_name=stack,
    )

    # Fetch tenant-workflow-level static config
    tenant_workflow_static_config = get_static_config(
        table=ConfigApiTable.TENANT_WORKFLOW,
        model=AnalyticsControllerTenantWorkflowStaticConfig,
        tenant_name=tenant,
        stack_name=stack,
        workflow_name=workflow,
    )

    # If lang id voice is enabled for the tenant, but not the tenant workflow, tenant workflow
    # should take precedence and it should not be enabled in the output.
    # If lang id voice is not enabled for the tenant, but is enabled for the tenant workflow,
    # it should be enabled in the output.
    tenant_workflow_lang_id_voice_flag = (
        tenant_workflow_static_config.enable_language_identification_voice
    )
    tenant_lang_id_voice_flag = tenant_static_config.enable_language_identification_voice
    language_id_voice_flag = tenant_lang_id_voice_flag or False
    # Overwrite the value from tenant with the value from tenant workflow if it present
    # (with either value True or False)
    if tenant_workflow_lang_id_voice_flag is not None:
        language_id_voice_flag = tenant_workflow_lang_id_voice_flag

    # Determine if the tenant has language detection enabled
    check_language_detection_result = check_language_detection(
        tenant_configuration=tenant_configuration, tenant=tenant
    )

    # Propagate IOParams to downstream Conductor Task
    finish_flow(
        result_path=result_path,
        is_lexica_enabled=is_lexica_enabled,
        is_language_detection_enabled=check_language_detection_result,
        is_language_identification_voice_enabled=language_id_voice_flag,
        transcription_provider=tenant_configuration.get("voice", {}).get("transcriptionProvider"),
        transcription_limit=tenant_configuration.get("contractualLimits", {})
        .get("volume", {})
        .get("voice", {})
        .get("transcription"),
        tenant_config_feature_flags=tenant_configuration.get("featureFlags", []),
    )
