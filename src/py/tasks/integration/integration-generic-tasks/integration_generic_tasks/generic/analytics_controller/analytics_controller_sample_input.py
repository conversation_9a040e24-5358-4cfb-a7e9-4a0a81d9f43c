import datetime
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test",
        name="focus_horizon_voice",
        stack="uat-shared-steeleye",
        tenant="ben",
        start_timestamp=datetime.datetime.utcnow(),
    )
    input_param = IOParamFieldSet(
        params=dict(
            file_uri="s3://ben.uat.steeleye.co/aries/ingress/nonstreamed/evented/focus_horizon_voice/emptycalls.zip",
        )
    )
    task = TaskFieldSet(name="analytics_controller", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
