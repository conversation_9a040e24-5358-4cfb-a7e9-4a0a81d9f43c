import hvac
import logging
import pandas as pd
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_comms_tasks.monitored_users.transcription.static import (
    TranscriptionForMonitoredParticipants,
)
from aries_se_comms_tasks.monitored_users.transcription.transcription_monitored_users import (
    filter_calls_by_transcription_for_monitored_users,
)
from aries_se_comms_tasks.transcription.app_metrics_enum import TranscriptionAppMetricsEnum
from aries_se_comms_tasks.transcription.auxiliary_tasks.create_transcription_feed_input import (
    run_create_transcription_feed_input,
)
from aries_se_comms_tasks.transcription.auxiliary_tasks.prepare_for_transcription import (
    run_prepare_for_transcription,
)
from aries_se_comms_tasks.transcription.retranscriber.retranscriber_factory import get_retranscriber
from aries_se_comms_tasks.transcription.retranscriber.retranscriber_task import (
    run_retranscriber_task,
)
from aries_se_comms_tasks.transcription.static import TranscriptionApiDerivedColumns
from aries_se_comms_tasks.transcription.transcriber.static import TranscriberTargetFields
from aries_se_comms_tasks.transcription.transcriber.transcriber_factory import (
    get_transcriber,
)
from aries_se_comms_tasks.transcription.transcriber.transcriber_task import (
    run_transcriber_task,
)
from aries_se_comms_tasks.transcription.transcription_splitter.static import (
    TranscriptionProviderStaticConfig,
)
from aries_se_comms_tasks.transcription.transcription_splitter.transcription_splitter import (
    TranscriptionSplitterResult,
    run_transcription_splitter,
)
from aries_se_comms_tasks.voice.static import CallColumns
from aries_se_core_tasks.aries.utility_tasks.finish_flow import finish_flow
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.datetime.get_seconds_from_time import Params as ParamsGetSecondsFromTime
from aries_se_core_tasks.datetime.get_seconds_from_time import run_get_seconds_from_time
from aries_se_core_tasks.frame.frame_concatenator import Params as ParamsFrameConcatenator
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.io.read.fetch_tenant_configuration import run_fetch_tenant_configuration
from aries_se_core_tasks.static_config.static_config import ConfigApiTable, get_static_config
from aries_task_link.models import AriesTaskInput
from integration_audio_comms_tasks.transcription.transcription_api.input_schema import (
    TranscriptionAPIAriesTaskInput,
)
from omegaconf import OmegaConf
from pathlib import Path
from se_core_tasks.frame.frame_concatenator import OrientEnum
from se_data_lake.cloud_utils import (
    get_bucket,
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
)
from se_data_lake.lake_path import (
    get_ingress_depository_lake_path_for_transcripts,
)
from se_elastic_schema.static.tenant_configuration import FeatureFlags
from se_elastic_schema.static.transcript import TranscriptionStatusEnum, VendorEnum
from se_enums.cloud import CloudProviderEnum
from se_io_utils.json_utils import ndjson_to_flat_dataframe, read_json
from se_secrets_client.utils import get_secrets, secrets_client
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)


class TranscriptionAPIFlow:
    """Base class for Deepgram API flow implementation."""

    @staticmethod
    def get_rows_to_transcribe(ndjson_file_path: str, tenant_configuration: Dict):
        """Prepare the source DataFrame for transcription."""
        # Read NDJSON and load its contents into a flattened DataFrame
        transcription_source_frame = ndjson_to_flat_dataframe(
            ndjson_file_path=ndjson_file_path,
            skip_on_emtpy_frame=False,
        )
        transcription_source_frame[
            [
                TranscriptionForMonitoredParticipants.SHOULD_BE_TRANSCRIBED,
                TranscriptionApiDerivedColumns.DURATION_IN_RANGE_FLAG,
            ]
        ] = True
        # Check if restricted transcription feature flag is enabled
        feature_flags = tenant_configuration.get("featureFlags", [])
        logger.info(f"Feature flags: {feature_flags}")

        # Apply filtering if the feature flag is present
        if FeatureFlags.RESTRICTED_TRANSCRIPTION in feature_flags:
            logger.info("Restricted transcription feature flag is enabled, filtering calls...")
            transcription_source_frame = filter_calls_by_transcription_for_monitored_users(
                transcription_source_frame
            )

        return transcription_source_frame

    def run_flow(
        self,
        aries_task_input: AriesTaskInput,
        app_metrics_path: Optional[str] = None,
        audit_path: Optional[str] = None,
        result_path: str = "result.json",
    ):
        # Parse and validate AriesTaskInput parameters
        task_transform_input = unpack_aries_task_input(
            aries_task_input=aries_task_input, model=TranscriptionAPIAriesTaskInput
        )

        # Get tenant workflow tenant config from postgres
        cached_tenant_workflow_config = CachedTenantWorkflowAPIClient.get(
            tenant_name=aries_task_input.workflow.tenant,
            workflow_name=aries_task_input.workflow.name,
            stack_name=aries_task_input.workflow.stack,
        )

        cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(
            file_uri=cached_tenant_workflow_config.tenant.lake_prefix
        )

        cloud_provider_prefix: str = get_cloud_provider_prefix(value=cloud_provider)

        streamed: bool = cached_tenant_workflow_config.workflow.streamed

        # Get tenant
        bucket: str = get_bucket(file_uri=task_transform_input.file_uri)
        tenant: str = aries_task_input.workflow.tenant
        stack: str = aries_task_input.workflow.stack

        # Get the TenantConfiguration document from ElasticSearch
        tenant_configuration = run_fetch_tenant_configuration(tenant=tenant)

        transcription_provider_static_config = get_static_config(
            table=ConfigApiTable.TENANT,
            model=TranscriptionProviderStaticConfig,
            tenant_name=tenant,
            stack_name=stack,
        )

        # Get the ___raw_success.ndjson filepath to extract all Call records
        ndjson_file_path = task_transform_input.file_uri

        transcription_source_frame = self.get_rows_to_transcribe(
            ndjson_file_path=ndjson_file_path, tenant_configuration=tenant_configuration
        )
        df_to_transcribe = run_prepare_for_transcription(
            source_frame=transcription_source_frame,
        )

        total_records_to_transcribe = df_to_transcribe.shape[0]
        transcription_results_list = []

        duration_in_seconds_df = run_get_seconds_from_time(
            source_frame=df_to_transcribe,
            params=ParamsGetSecondsFromTime(
                source_time_attribute=CallColumns.CALL_DURATION,
                source_time_format="%H:%M:%S",
                target_attribute=TranscriptionApiDerivedColumns.CALL_DURATION_IN_SECONDS,
            ),
        )

        # Concatenate the new call_duration column to the DataFrame with all data needed
        # for transcription
        intermediate_frame = run_frame_concatenator(
            params=ParamsFrameConcatenator(
                orient=OrientEnum.horizontal.value, drop_columns=[CallColumns.CALL_DURATION]
            ),
            source_frame=df_to_transcribe,
            duration_in_seconds=duration_in_seconds_df,
        )

        # Route to different transcription providers based on the detected languages
        # (if there are detected languages), and get a dict of splitter results
        # for both the supported transcription providers (Deepgram and Fano)
        transcription_splitter_result = run_transcription_splitter(
            tenant=tenant,
            cell_name=stack,
            workflow_name=aries_task_input.workflow.name,
            source_frame=intermediate_frame,
            transcription_provider_static_config=transcription_provider_static_config,
        )
        deepgram_result_df = transcribe_and_retranscribe(
            splitter_result=transcription_splitter_result,
            transcription_provider=VendorEnum.DEEP_GRAM,
            aries_task_input=aries_task_input,
            bucket=bucket,
            streamed=streamed,
            cloud_provider=cloud_provider,
            cloud_provider_prefix=cloud_provider_prefix,
            tenant_configuration=tenant_configuration,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )
        if deepgram_result_df is not None:
            transcription_results_list.append(deepgram_result_df)

        fano_result_df = transcribe_and_retranscribe(
            splitter_result=transcription_splitter_result,
            transcription_provider=VendorEnum.FANO,
            aries_task_input=aries_task_input,
            bucket=bucket,
            streamed=streamed,
            cloud_provider=cloud_provider,
            cloud_provider_prefix=cloud_provider_prefix,
            tenant_configuration=tenant_configuration,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
        )
        if fano_result_df is not None:
            transcription_results_list.append(fano_result_df)

        # Create a CSV file which will be the input of the transcription_feed Flow
        output_file_path = run_create_transcription_feed_input(
            transcription_result_frames=transcription_results_list,
            aries_task_input=aries_task_input,
            bucket=bucket,
            cloud_provider_prefix=cloud_provider_prefix,
            transcription_flow_input_path=ndjson_file_path,
        )

        fano_and_deepgram_counts = read_json(path=app_metrics_path)
        finish_flow(
            result_path=result_path,
            file_uri=output_file_path,
            stats={
                "Total records to transcribe": total_records_to_transcribe,
                "Successful Deepgram transcripts": fano_and_deepgram_counts.get(
                    TranscriptionAppMetricsEnum.DEEPGRAM_SUCCESSFUL_TRANSCRIPT_COUNT.value, 0
                ),
                "Successful Fano transcripts": fano_and_deepgram_counts.get(
                    TranscriptionAppMetricsEnum.FANO_SUCCESSFUL_TRANSCRIPT_COUNT.value, 0
                ),
                **get_stats(lst=transcription_results_list),
            },
        )


def transcribe_and_retranscribe(
    splitter_result: Dict[str, TranscriptionSplitterResult],
    transcription_provider: VendorEnum,
    aries_task_input: AriesTaskInput,
    bucket: str,
    streamed: bool,
    cloud_provider: CloudProviderEnum,
    cloud_provider_prefix: str,
    tenant_configuration: dict,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
) -> pd.DataFrame | None:
    """
    Transcribes and re-transcribes (if necessary) for the given transcription
    provider.
    """
    splitter_result = splitter_result.get(transcription_provider)
    if splitter_result and not splitter_result.dataframe.empty:
        transcriber_params_list = splitter_result.transcriber_params_list
        transcriber_source_frame = splitter_result.dataframe

        # Derive the upload target prefix/path for the transcripts
        transcript_upload_path: str = get_ingress_depository_lake_path_for_transcripts(
            workflow_name=aries_task_input.workflow.name,
            workflow_start_timestamp=aries_task_input.workflow.start_timestamp,
            transcription_provider=transcription_provider,
        )
        transcriber_kwargs = {}
        # Build kwargs for fano -- fano token and base url
        if transcription_provider == VendorEnum.FANO:
            # Retrieve secrets from Vault
            config = OmegaConf.load(Path(__file__).parent.joinpath("config.yml"))
            try:
                secrets = get_secrets(
                    secret_client=secrets_client(config.vault, config),
                    tenant_name=aries_task_input.workflow.tenant,
                    workflow_name="fano",
                    poller_type="api",
                )
            except hvac.exceptions.InvalidPath:
                raise ValueError(
                    "Fano bearer token not present in the expected Vault path."
                    "Make sure it is present before trying again"
                )

            transcriber_kwargs["bearer_token"] = secrets.fano_bearer_token
            base_fano_url = secrets.base_fano_url
            transcriber_kwargs["base_fano_url"] = base_fano_url
        # Run Transcription. Upload resulting .json transcript files to cloud and produce
        # a DataFrame with the summary of the transcription results.
        # We get a list of params from run_get_transcriber_params,
        # this task has to execute or each set of params
        # NOTE: transcriber_kwargs are used by Fano and have the token
        transcriber = get_transcriber(
            transcription_provider=transcription_provider, **transcriber_kwargs
        )
        retranscriber = get_retranscriber(transcription_provider=transcription_provider)

        for transcriber_params in transcriber_params_list:
            transcriber_result_df = run_transcriber_task(
                transcriber=transcriber,
                source_frame=transcriber_source_frame,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
                transcription_provider=transcription_provider,
                tenant_configuration=tenant_configuration,
                transcriber_params=transcriber_params,
                bucket=bucket,
                streamed=streamed,
                transcript_upload_path=transcript_upload_path,
                cloud_provider=cloud_provider,
                cloud_provider_prefix=cloud_provider_prefix,
            )

            retranscriber_result_df = run_retranscriber_task(
                transcriber=transcriber,
                retranscriber=retranscriber,
                source_frame=transcriber_result_df,
                tenant_configuration=tenant_configuration,
                bucket=bucket,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
                streamed=streamed,
                transcript_upload_path=transcript_upload_path,
                cloud_provider=cloud_provider,
                cloud_provider_prefix=cloud_provider_prefix,
            )
            return retranscriber_result_df


def get_stats(lst: List[pd.DataFrame]) -> dict:
    stats: Dict[str, int] = {stat: "N/A" for stat in TranscriptionStatusEnum.list()}

    for df in lst:
        for key, value in stats.items():
            if isinstance(stats[key], str):
                stats[key] = df[df[TranscriberTargetFields.TRANSCRIPTION_STATUS] == key].shape[0]
            else:
                stats[key] += df[df[TranscriberTargetFields.TRANSCRIPTION_STATUS] == key].shape[0]

    return stats


def transcription_api_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: Optional[str] = None,
    audit_path: Optional[str] = None,
    result_path: str = "result.json",
):
    flow = TranscriptionAPIFlow()
    return flow.run_flow(
        aries_task_input=aries_task_input,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
        result_path=result_path,
    )
