from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from datetime import datetime
from se_elastic_schema.static.tenant_configuration import FeatureFlags


def sample_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_transcription_copilot_1",
        name="cloud9_voice",
        stack="uat-shared-steeleye",
        tenant="ben",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
            params={
              "tenant_config_feature_flags": [
                "call-transcription-copilot",
                "self-hosted-llm"
              ],
              "start_date_time": "2025-08-21T12:06:03",
              "end_date_time": "2025-08-22T13:03:00"
            }
    )
    task = TaskFieldSet(name="transcription_copilot_flow", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
