from typing import Optional

import addict
import datetime
import logging
import pandas as pd
import se_schema_meta
from aries_config_api_compatible_client.tenant_workflow import CompatibleTenantWorkflowAPIClient
from aries_se_api_client.client import AriesApiClient
from aries_se_comms_tasks.transcription.static import TranscriptModelFields
from aries_se_comms_tasks.transcription.transcript_copilot.copilot_call_frame_with_transcript_fields import (  # noqa E501
    TempColumns,
    run_copilot_call_frame_with_transcript_fields,
)
from aries_se_comms_tasks.transcription.transcript_copilot.transcript_copilot import (
    TranscriptCopilot,
    run_transcript_copilot,
)
from aries_se_comms_tasks.voice.static import CallColumns, SemanticModelFields
from aries_se_core_tasks.aries.utility_tasks.finish_flow import (
    add_nested_params,
    finish_flow,
)
from aries_se_core_tasks.aries.utility_tasks.unpack_aries_task_input import unpack_aries_task_input
from aries_se_core_tasks.datetime.get_seconds_from_time import run_get_seconds_from_time, Params as GetSecondsFromTimeParams
from aries_se_core_tasks.frame.filter_columns import (  # type: ignore[attr-defined]
    Params as ParamsFilterColumns,
)
from aries_se_core_tasks.frame.filter_columns import run_filter_columns
from aries_se_core_tasks.frame.frame_concatenator import (  # type: ignore[attr-defined]
    Params as ParamsFrameConcatenator,
)
from aries_se_core_tasks.frame.frame_concatenator import run_frame_concatenator
from aries_se_core_tasks.io.create_ndjson_path import create_ndjson_path
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.fetch_tenant_configuration import run_fetch_tenant_configuration
from aries_se_core_tasks.io.write.write_ndjson import run_write_ndjson
from aries_se_core_tasks.static import MetaModel
from aries_se_core_tasks.static_config.static_config import get_static_config, ConfigApiTable
from aries_se_core_tasks.utilities.elasticsearch_utils import get_es_config
from aries_se_core_tasks.utilities.serializer import serializer
from aries_task_link.models import AriesTaskInput
from copilot_utils.client import SeOpenApiClient
from data_platform_config_api_client.tenant_workflow import TenantWorkflowAPI
from integration_audio_comms_tasks.transcription.transcription_copilot.input_schema import (
    TranscriptionCopilotAriesTaskInput, TranscriptionCopilotStaticConfig,
)
from integration_audio_comms_tasks.transcription.transcription_copilot.queries import (
    fetch_transcribed_calls_which_need_copilot_enrichment,
    get_spoken_neural_watches,
    get_transcripts,
)
from omegaconf import DictConfig, ListConfig, OmegaConf
from pathlib import Path
from se_core_tasks.frame.filter_columns import ActionEnum
from se_core_tasks.frame.frame_concatenator import OrientEnum

from integration_audio_comms_tasks.transcription.transcription_copilot.static import TranscriptionTempColumns
from se_data_lake.cloud_utils import (
    get_cloud_provider_from_file_uri,
    get_cloud_provider_prefix,
    get_tenant_bucket_from_workflow,
)
from se_elastic_schema.models import Call, Transcript
from se_elastic_schema.static.tenant_configuration import FeatureFlags
from se_elasticsearch.repository import get_repository_by_cluster_version
from se_elasticsearch.repository.elasticsearch8 import ElasticsearchRepository
from se_enums.cloud import CloudProviderEnum
from se_enums.elastic_search import EsActionEnum
from se_io_utils.json_utils import ndjson_to_flat_dataframe

logger = logging.getLogger(__name__)


@serializer
def deduplicate_columns(df, positional_keep="first"):
    return df.loc[:, ~df.columns.duplicated(keep=positional_keep)]


@serializer
def is_required_copilot_columns_absent(transcript_copilot_df) -> bool:
    if transcript_copilot_df.empty or transcript_copilot_df.shape[0] == 0:
        logger.info("Skipping batch due to empty transcript copilot result")
        return True

    # Check if all required columns are empty
    str_df = transcript_copilot_df[TranscriptCopilot.REQUIRED_COLUMNS].astype(str)

    # Combine all empty value masks
    empty_mask = (
        (str_df == "None")
        | (str_df == "[]")
        | (str_df == "")
        | (str_df == "nan")
        | (str_df == "<NA>")
    )

    if empty_mask.all().all():
        logger.info("Skipping batch due to empty required columns in transcript copilot result")
        return True
    else:
        return False


def resolve_date_ranges(
    aries_task_input: AriesTaskInput,
    config: DictConfig | ListConfig,
    workflow_last_execution_time: datetime.datetime | None,
) -> tuple[datetime.datetime, datetime.datetime]:
    # Define the upper bound date
    start_date_time_override = aries_task_input.input_param.params.get("start_date_time")
    end_date_time_override = aries_task_input.input_param.params.get("end_date_time")

    current_datetime = datetime.datetime.now(datetime.UTC)

    # Convert to valid date-times
    if start_date_time_override:
        start_date_time_override = datetime.datetime.strptime(
            start_date_time_override, "%Y-%m-%dT%H:%M:%S"
        ).replace(tzinfo=datetime.timezone.utc)
    elif workflow_last_execution_time:
        start_date_time_override = workflow_last_execution_time
    else:
        start_date_time_override = current_datetime - datetime.timedelta(
            days=int(config.lookback_days)
        )

    end_date_time_override = (
        datetime.datetime.strptime(end_date_time_override, "%Y-%m-%dT%H:%M:%S").replace(
            tzinfo=datetime.timezone.utc
        )
        if end_date_time_override
        else current_datetime
    )

    logger.info(
        f"Resolved start date: {start_date_time_override}; end date: {end_date_time_override}"
    )

    return start_date_time_override, end_date_time_override

def filter_transcripts_by_duration(
    call_frame: pd.DataFrame,
    transcript_frame: pd.DataFrame,
    cloud_provider_prefix: str,
    static_config: TranscriptionCopilotStaticConfig,
    file_based: Optional[bool],
):
    """
    File-based:
      - Annotate transcript rows with SKIP_COPILOT=True by default, False for eligible.
      - Do NOT drop rows (keeps file shape).

    ES-based:
      - Drop ineligible rows from BOTH frames to ensure no empty/placeholder docs reach ES.
    """
    required_call_columns = [
        CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET,
        CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY,
        CallColumns.CALL_DURATION,
    ]
    duration_threshold_seconds = static_config.transcription_copilot_duration_threshold

    # If no duration threshold is configured, skip duration filtering
    if duration_threshold_seconds is None:
        logger.info("No transcription_copilot_duration_threshold configured. Skipping duration filtering.")
        return call_frame, transcript_frame

    # If we can’t compute duration, keep original frames as-is.
    if not all(col in call_frame.columns for col in required_call_columns):
        logger.warning(
            f"Required call columns {required_call_columns} not found. Skipping duration filtering."
        )
        return call_frame, transcript_frame

    duration_not_null_mask = call_frame[CallColumns.CALL_DURATION].notnull()
    if not duration_not_null_mask.any():
        logger.warning("All call durations are null. Skipping duration filtering.")
        return call_frame, transcript_frame

    required_call_df = call_frame.loc[duration_not_null_mask, required_call_columns].copy()
    required_call_df.loc[:, TranscriptionTempColumns.RECORDING_SOURCE_KEY] = (
        cloud_provider_prefix
        + required_call_df.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET]
        + "/"
        + required_call_df.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY]
    )

    call_frame_with_seconds = run_get_seconds_from_time(
        source_frame=required_call_df,
        params=GetSecondsFromTimeParams(
            source_time_attribute=CallColumns.CALL_DURATION,
            target_attribute=TranscriptionTempColumns.CALL_DURATION_IN_SECONDS,
            source_time_format="%H:%M:%S",
        ),
        skip_serializer=True,
    )

    duration_seconds_col = pd.to_numeric(
        call_frame_with_seconds[TranscriptionTempColumns.CALL_DURATION_IN_SECONDS],
        errors="coerce",
    )
    duration_gte_threshold_mask = duration_seconds_col.notnull() & (
        duration_seconds_col >= duration_threshold_seconds
    )

    eligible_keys = required_call_df.loc[
        duration_gte_threshold_mask, TranscriptionTempColumns.RECORDING_SOURCE_KEY
    ].tolist()

    # Build mask on transcripts via recordingSourceKey
    if TranscriptModelFields.RECORDING_SOURCE_KEY not in transcript_frame.columns:
        logger.warning(
            f"{TranscriptModelFields.RECORDING_SOURCE_KEY} not present in transcript_frame; "
            f"skipping duration filtering."
        )
        return call_frame, transcript_frame

    if file_based:
        transcript_frame[TranscriptionTempColumns.SKIP_COPILOT] = False
        transcript_mask = transcript_frame[
            TranscriptModelFields.RECORDING_SOURCE_KEY
        ].isin(eligible_keys)
        transcript_frame.loc[~transcript_mask, TranscriptionTempColumns.SKIP_COPILOT] = True
        return call_frame, transcript_frame
    else:
        # ES-based: either mark SKIP_INGESTION then drop, or just drop directly.
        # We drop directly here to guarantee no-ops at ES writer.
        transcript_mask = transcript_frame[
            TranscriptModelFields.RECORDING_SOURCE_KEY
        ].isin(eligible_keys)
        # Drop ineligible transcripts
        transcript_frame = transcript_frame.loc[transcript_mask]

        # Also drop ineligible calls to avoid writing updates for them
        # (ensure we drop using the same eligible key set)
        call_frame.loc[:, TranscriptionTempColumns.RECORDING_SOURCE_KEY] = (
            cloud_provider_prefix
            + call_frame.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET].astype(str)
            + "/"
            + call_frame.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY].astype(str)
        )
        call_mask = call_frame[TranscriptionTempColumns.RECORDING_SOURCE_KEY].isin(eligible_keys)
        call_frame = call_frame.loc[call_mask]

        transcript_mask = transcript_frame[
            TranscriptModelFields.RECORDING_SOURCE_KEY
        ].isin(eligible_keys)
        transcript_frame.loc[transcript_mask, TranscriptionTempColumns.SKIP_COPILOT] = False

        return call_frame, transcript_frame



def process_batch(
    tenant,
    aries_task_input,
    cloud_provider_prefix,
    tenant_bucket_with_cloud_prefix,
    transcript_source_frame,
    call_source_frame,
    config: DictConfig | ListConfig,
    app_metrics_path,
    audit_path,
    tenant_configuration: dict | None = None,
    suffix: str | None = None,
    index_transcripts: bool = True,
):
    # BUSINESS LOGIC #
    transcript_final_frame = transcript_source_frame
    call_final_frame = call_source_frame

    if not transcript_source_frame.empty and not call_source_frame.empty:
        # Run Transcript Copilot task
        transcript_copilot_result = run_transcript_copilot(
            source_frame=transcript_source_frame,
            config=config,
            tenant=tenant,
            workflow=aries_task_input.workflow.name,
            tenant_configuration=tenant_configuration,
        )

        # Check if all required columns are empty for ES-based flow.
        # This is done because only for the ES-based flow as respective
        # data is already ingested into ES, and we do not want to ingest it again.
        if not index_transcripts and is_required_copilot_columns_absent(
            transcript_copilot_df=transcript_copilot_result
        ):
            return None, None

        # Concatenate transcript_copilot_result and transcript_source_frame
        transcript_frame_with_copilot = run_frame_concatenator(
            params=ParamsFrameConcatenator(
                orient=OrientEnum.horizontal.value,
            ),
            source_frame=transcript_source_frame,
            transcript_copilot_frame=transcript_copilot_result,
        )

        # Merge in required fields from transcript_df to the call_df
        call_final_frame = run_copilot_call_frame_with_transcript_fields(
            call_frame=call_source_frame,
            transcript_frame_with_copilot=transcript_frame_with_copilot,
            cloud_provider_prefix=cloud_provider_prefix,
        )
        # Keeping the original co-pilot run data in case
        # we get duplicated columns due to a current co-pilot run.
        # This is a rare edge case and should not happen frequently
        call_final_frame = deduplicate_columns(df=call_final_frame)

        # Drop non-schema columns from transcript_df now that we have
        # moved them to the Call model
        transcript_final_frame = run_filter_columns(
            source_frame=transcript_frame_with_copilot,
            params=ParamsFilterColumns(
                action=ActionEnum.drop,
                columns=[
                    CallColumns.ANALYTICS_COPILOT_ANALYTICS_RISKS,
                    CallColumns.ANALYTICS_COPILOT_ANALYTICS_METADATA,
                ]
                + SemanticModelFields.list(),
            ),
        )

    # Create the path where the Transcript ndjson result is to be uploaded
    transcript_ndjson_path = create_ndjson_path(
        tenant_bucket=tenant_bucket_with_cloud_prefix,
        aries_task_input=aries_task_input,
        model=MetaModel.TRANSCRIPT,
        suffix=suffix,
    )

    # Write the transcript_final_frame into a ndjson file at the generated ndjson path
    run_write_ndjson(
        source_serializer_result=transcript_final_frame,
        output_filepath=transcript_ndjson_path,
        audit_output=True,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )
    transcript_output = add_nested_params(
        file_uri=transcript_ndjson_path,
        es_action=EsActionEnum.INDEX if index_transcripts else EsActionEnum.UPDATE,
        data_model=Transcript.get_reference().get_qualified_reference(),
        ignore_empty_file_uri=True,
    )

    # Create the path where the Call ndjson result is to be uploaded
    call_ndjson_path = create_ndjson_path(
        tenant_bucket=tenant_bucket_with_cloud_prefix,
        aries_task_input=aries_task_input,
        model=MetaModel.CALL,
        suffix=suffix,
    )

    # Write the call_final_frame into a ndjson file at the generated ndjson path
    run_write_ndjson(
        source_serializer_result=call_final_frame,
        output_filepath=call_ndjson_path,
        audit_output=True,
        app_metrics_path=app_metrics_path,
        audit_path=audit_path,
    )
    call_output = add_nested_params(
        file_uri=call_ndjson_path,
        es_action=EsActionEnum.UPDATE,
        data_model=Call.get_reference().get_qualified_reference(),
        ignore_empty_file_uri=True,
    )

    return transcript_output, call_output


def transcription_copilot_flow(
    aries_task_input: AriesTaskInput,
    app_metrics_path: str,
    audit_path: str,
    result_path: str = "result.json",
):
    # SETUP #
    # Initialize the SeOpenApiClient client here with force_override
    # to reset it across multiple task run in the same worker
    SeOpenApiClient(force_override=True)

    # Parse and validate AriesTaskInput parameters
    transcription_copilot_flow_input = unpack_aries_task_input(
        aries_task_input=aries_task_input, model=TranscriptionCopilotAriesTaskInput
    )
    transcript_source_file_path = transcription_copilot_flow_input.Transcript.get("params", {}).get(
        "file_uri"
    )
    call_source_file_path = transcription_copilot_flow_input.Call.get("params", {}).get("file_uri")

    tenant = aries_task_input.workflow.tenant
    workflow = aries_task_input.workflow.name
    stack: str = aries_task_input.workflow.stack

    # Load the file containing configs
    config = OmegaConf.load(Path(__file__).parent.joinpath("config.yml"))

    tenant_workflow_api = TenantWorkflowAPI(
        AriesApiClient(host=config.data_platform_config_api_url)
    )

    # get tenant workflow configuration for destination workflow
    tenant_workflow_config = CompatibleTenantWorkflowAPIClient.get(
        tenant_workflow_api=tenant_workflow_api,
        tenant_name=tenant,
        workflow_name=workflow,
    )

    file_uri = tenant_workflow_config.tenant.lake_prefix

    cloud_provider: CloudProviderEnum = get_cloud_provider_from_file_uri(file_uri)
    cloud_provider_prefix = get_cloud_provider_prefix(cloud_provider)

    tenant_bucket_with_cloud_prefix: str = get_tenant_bucket_from_workflow(
        workflow=aries_task_input.workflow, cloud_provider_enum=cloud_provider
    )

    static_config = get_static_config(table=ConfigApiTable.TENANT_WORKFLOW,model=TranscriptionCopilotStaticConfig,tenant_name=tenant,workflow_name=workflow,stack_name=stack,)

    # Fetch TenantConfig feature flags if it's not provided
    if not transcription_copilot_flow_input.tenant_config_feature_flags:
        tenant_configuration = run_fetch_tenant_configuration(
            tenant=tenant,
        )
        transcription_copilot_flow_input.tenant_config_feature_flags = tenant_configuration.get(
            "featureFlags", []
        )
    else:
        tenant_configuration = addict.Dict(
            {"featureFlags": transcription_copilot_flow_input.tenant_config_feature_flags}
        )

    file_uri_check = transcript_source_file_path and call_source_file_path

    if (
        FeatureFlags.CALL_TRANSCRIPTION_COPILOT.value
        not in transcription_copilot_flow_input.tenant_config_feature_flags
    ) or (
        file_uri_check
        and FeatureFlags.SELF_HOSTED_LLM
        in transcription_copilot_flow_input.tenant_config_feature_flags
    ):
        logger.warning(f"Tenant: {tenant} not eligible for copilot, Finishing flow")
        transcript_output = add_nested_params(
            file_uri=transcript_source_file_path,
            es_action=EsActionEnum.CREATE,
            data_model="se_elastic_schema.models.tenant.communication.transcript:Transcript",
            ignore_empty_file_uri=True,
        )
        call_output = add_nested_params(
            file_uri=call_source_file_path,
            es_action=EsActionEnum.UPDATE,
            data_model="se_elastic_schema.models.tenant.communication.call:Call",
            ignore_empty_file_uri=True,
        )

        finish_flow(
            result_path=result_path,
            result_data={
                MetaModel.TRANSCRIPT: transcript_output,
                MetaModel.CALL: call_output,
            },
        )
        return

    # Supports two modes:
    # 1. File URI–based: Updates Call (already in ES), indexes Transcript (not yet in ES).
    # 2. Scheduled (non-File URI): Updates both Call and Transcript as they already exist in ES.
    # In File URI mode, inputs are passed through on Copilot failure; in ES mode,
    # they're skipped to avoid duplicate updates.
    # Determine data source: File-based or Elasticsearch-based
    if file_uri_check:
        # == FILE-BASED DATA LOADING ==
        logger.info("File URIs provided. Starting file-based processing.")

        # Download remote files from cloud
        transcript_local_file_path = run_download_file(file_url=transcript_source_file_path)
        call_local_file_path = run_download_file(file_url=call_source_file_path)

        # read source data for transcript and call into separate dataframes
        transcript_source_frame = ndjson_to_flat_dataframe(
            ndjson_file_path=transcript_local_file_path,
            skip_on_emtpy_frame=False,
        )
        call_source_frame = ndjson_to_flat_dataframe(
            ndjson_file_path=call_local_file_path,
            skip_on_emtpy_frame=False,
        )

        if static_config:
            call_source_frame, transcript_source_frame = filter_transcripts_by_duration(
                call_frame=call_source_frame,
                transcript_frame=transcript_source_frame,
                static_config=static_config,
                cloud_provider_prefix=cloud_provider_prefix,
                file_based=True,
            )

        transcript_output, call_output = process_batch(
            tenant=tenant,
            aries_task_input=aries_task_input,
            cloud_provider_prefix=cloud_provider_prefix,
            tenant_bucket_with_cloud_prefix=tenant_bucket_with_cloud_prefix,
            transcript_source_frame=transcript_source_frame,
            call_source_frame=call_source_frame,
            config=config,
            app_metrics_path=app_metrics_path,
            audit_path=audit_path,
            tenant_configuration=tenant_configuration,
        )

        # finish the flow with both transcript and call results
        finish_flow(
            result_path=result_path,
            result_data={
                MetaModel.TRANSCRIPT: transcript_output,
                MetaModel.CALL: call_output,
            },
        )
    else:
        # == ELASTICSEARCH-BASED DATA LOADING ==
        input_calls = 0
        skipped_calls = 0

        logger.info("File URIs not provided. Querying Elasticsearch for calls to process.")
        es_client: ElasticsearchRepository = get_repository_by_cluster_version(
            resource_config=get_es_config()
        )

        workflow_last_execution_time = (
            datetime.datetime.fromisoformat(
                tenant_workflow_config.workflow_last_executed
            ).astimezone(datetime.timezone.utc)
            if tenant_workflow_config.workflow_last_executed
            else None
        )

        start_date_time, end_date_time = resolve_date_ranges(
            aries_task_input=aries_task_input,
            config=config,
            workflow_last_execution_time=workflow_last_execution_time,
        )
        all_calls = fetch_transcribed_calls_which_need_copilot_enrichment(
            batch_size=int(config.batch_size),
            start_date_time=start_date_time,
            end_date_time=end_date_time,
            tenant=tenant,
            es_client=es_client,
        )

        output_list = list()

        for idx, call_batch in enumerate(all_calls, start=1):
            if not call_batch:
                logger.warning(
                    f"No calls found in '{tenant}' that require Copilot processing. Skipping batch."
                )
                continue

            call_source_frame = pd.json_normalize(call_batch)
            logger.info(f"Found {len(call_source_frame)} calls to process.")
            input_calls += len(call_source_frame)

            # Collect unique recordingSourceKey from calls to fetch corresponding transcripts
            call_source_frame.loc[:, TempColumns.CALL_RECORDING_SOURCE_KEY] = (
                cloud_provider_prefix
                + call_source_frame.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_BUCKET]
                + "/"
                + call_source_frame.loc[:, CallColumns.VOICE_FILE_FILE_INFO_LOCATION_KEY]
            )

            recording_source_keys = (
                call_source_frame[TempColumns.CALL_RECORDING_SOURCE_KEY].dropna().unique().tolist()
            )

            if not recording_source_keys:
                logger.warning(
                    f"Calls found, but no associated "
                    f"{TranscriptModelFields.RECORDING_SOURCE_KEY}. "
                    f"Cannot fetch transcripts."
                )
                transcript_source_frame = pd.DataFrame()
                skipped_calls += len(call_source_frame)
            else:
                logger.info(
                    f"Fetching transcripts for {len(recording_source_keys)} "
                    f"unique {TranscriptModelFields.RECORDING_SOURCE_KEY}."
                )
                transcripts = get_transcripts(
                    tenant=tenant, recording_source_keys=recording_source_keys, es_client=es_client
                )
                transcript_source_frame = pd.json_normalize(transcripts)

                # In case we have multiple transcriptions with the same recordingSourceKey
                # we keep the one which is the latest
                transcript_source_frame = transcript_source_frame.drop_duplicates(
                    subset=[TranscriptModelFields.RECORDING_SOURCE_KEY]
                )
                logger.info(f"Found {len(transcript_source_frame)} matching transcripts.")
                skipped_calls += max(0, (len(call_source_frame) - len(transcript_source_frame)))

            call_source_frame, transcript_source_frame = filter_transcripts_by_duration(
                    call_frame=call_source_frame,
                    transcript_frame=transcript_source_frame,
                    static_config=static_config,
                    cloud_provider_prefix=cloud_provider_prefix,
                    file_based=False,
                )

            transcript_output, call_output = process_batch(
                tenant=tenant,
                aries_task_input=aries_task_input,
                cloud_provider_prefix=cloud_provider_prefix,
                tenant_bucket_with_cloud_prefix=tenant_bucket_with_cloud_prefix,
                transcript_source_frame=transcript_source_frame,
                call_source_frame=call_source_frame,
                config=config,
                app_metrics_path=app_metrics_path,
                audit_path=audit_path,
                suffix=f"batch_{idx}",
                index_transcripts=False,
                tenant_configuration=tenant_configuration,
            )

            # Only add to output list if process_batch didn't return None (empty required columns)
            if transcript_output is not None and call_output is not None:
                output_list.append(
                    {
                        MetaModel.CALL: call_output,
                        MetaModel.TRANSCRIPT: transcript_output,
                    }
                )

        # Fetch all spoken neural watches. It would be triggered downstream
        # once transcription copilot ingested has completed
        spoken_neural_watches = get_spoken_neural_watches(es_client=es_client, tenant=tenant)
        watch_output = [
            dict(io_param=dict(params=dict(watch_id=watch[se_schema_meta.ID])))
            for watch in spoken_neural_watches
        ]

        # Update last execution in config DB only if
        # current execution greater than previous last execution
        formatted_end_date_time = end_date_time.astimezone(datetime.timezone.utc).isoformat()

        if end_date_time > (
            workflow_last_execution_time
            if workflow_last_execution_time
            else datetime.datetime.min.replace(tzinfo=datetime.UTC)
        ):
            CompatibleTenantWorkflowAPIClient.update(
                tenant_workflow_api=tenant_workflow_api,
                json_body={"workflow_last_executed": formatted_end_date_time},
                tenant_name=tenant,
                workflow_name=workflow,
            )

        # Propagate current upper bound if last execution time is
        # greater than current upper bound
        if end_date_time < (
            workflow_last_execution_time
            if workflow_last_execution_time
            else datetime.datetime.now(datetime.UTC)
        ):
            formatted_end_date_time = (
                workflow_last_execution_time.isoformat()
                if workflow_last_execution_time
                else datetime.datetime.now(datetime.UTC).isoformat()
            )

        logger.info(
            f"Out of total {input_calls} calls {skipped_calls} "
            f"calls were skipped due to missing transcripts"
        )

        # Finish the flow with both transcript and call results
        # This is used downstream in a dynamic fork join
        finish_flow(
            result_path=result_path,
            result_data=dict(
                io_params_list=output_list,
                spoken_neural_watches_list=watch_output,
                upper_bound_date=formatted_end_date_time,
            ),
        )

    # Removing reference to current instance of `SeOpenApiClient`
    # so that current metrics is not cumulatively added to another task run
    SeOpenApiClient.destroy()
