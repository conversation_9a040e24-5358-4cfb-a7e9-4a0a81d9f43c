import addict
import boto3
import fsspec
import httpx
import json
import os
import pandas as pd
import pytest
import requests_mock
from aries_config_cached_client.tenant_workflow import CachedTenantWorkflowAPIClient
from aries_se_api_client.base import ParsedResponse
from aries_se_comms_tasks.transcription.auxiliary_tasks import prepare_for_transcription
from aries_se_comms_tasks.transcription.auxiliary_tasks.create_transcription_feed_input import (
    CreateTranscriptionFeedInput,
)
from aries_se_comms_tasks.transcription.transcriber import (
    deepgram_transcriber,
    fano_transcriber,
    transcriber_task,
)
from aries_se_core_tasks.io.read.cloud.download_file import run_download_file
from aries_se_core_tasks.io.read.fetch_tenant_configuration import FetchTenantConfiguration
from aries_se_core_tasks.static_config import static_config
from aries_task_link.models import AriesTaskInput
from data_platform_config_api_client.translation_config import TranslationConfigAPI
from freezegun import freeze_time
from http import HTTPStatus
from integration_audio_comms_tasks.transcription.transcription_api import (
    transcription_api_flow,
    transcription_api_task,
)
from integration_test_utils.aws_helpers.patch_response import (
    mock_aiobotocore_convert_to_response_dict,
)
from integration_wrapper.static import IntegrationAriesTaskVariables
from mock.mock import MagicMock
from moto import mock_aws
from pathlib import Path
from se_elastic_schema.static.transcript import TranscriptionStatusEnum, VendorEnum
from se_elasticsearch.repository.models import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_io_utils.json_utils import read_json, write_named_temporary_json
from se_io_utils.tempfile_utils import tmp_directory
from shutil import rmtree

TEMP_DIR: Path = tmp_directory()
AUDIT_PATH: Path = TEMP_DIR.joinpath("audit.json")

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
LOCAL_BUCKETS_PATH = DATA_PATH.joinpath("buckets")
EXPECTED_RESULTS_PATH = DATA_PATH.joinpath("expected_results")

EXPECTED_RESULT_ONLY_RECORDINGS_METADATA = EXPECTED_RESULTS_PATH.joinpath(
    "expected_result_only_recordings_metadata.csv"
)
EXPECTED_RESULTS_FILTERED_MONITORED_USER = EXPECTED_RESULTS_PATH.joinpath(
    "expected_result_filtered_monitored_users.csv"
)
EXPECTED_RESULTS_NO_MONITORED_USER = EXPECTED_RESULTS_PATH.joinpath(
    "expected_result_no_monitored_user.csv"
)
EXPECTED_RESULTS_FILTERED_BY_DURATION = EXPECTED_RESULTS_PATH.joinpath(
    "expected_result_filtered_by_duration.csv"
)

EXPECTED_OUTPUT_CSV_DEEPGRAM_ONLY = EXPECTED_RESULTS_PATH.joinpath(
    "expected_result_end_to_end_dg_only.csv"
).as_posix()
OUTPUT_CSV = EXPECTED_RESULTS_PATH.joinpath("result_transformed.csv").as_posix()


DEEPGRAM_API_RESPONSES_PATH = DATA_PATH.joinpath("deepgram_api_responses")
FANO_API_RESPONSES_PATH = DATA_PATH.joinpath("fano_api_responses")


SERIALIZER_TMP_DIR = CURRENT_PATH.joinpath("serializer_tmp_dir")
SERIALIZER_TMP_DIR.mkdir(parents=True, exist_ok=True)

os.environ[IntegrationAriesTaskVariables.SERIALIZER_TMP_DIR] = SERIALIZER_TMP_DIR.as_posix()

BUCKET_NAME: str = "test.dev.steeleye.co"


mock_aiobotocore_convert_to_response_dict()


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(SERIALIZER_TMP_DIR)
        rmtree(TEMP_DIR)
        CURRENT_PATH.joinpath("result.json").unlink(missing_ok=True)

    request.addfinalizer(_end)


def mock_es(mocker, module, scroll_result: pd.DataFrame):
    # Mocks for ElasticSearch
    mock_get_es_config = mocker.patch.object(module, "get_es_config")
    mock_get_es_config.return_value = ResourceConfig(
        host="localhost",
        port="9200",
        scheme="http",
        meta_prefix=MetaPrefix.AMPERSAND,
    )

    mock_es_repo = mocker.patch.object(module, "get_repository_by_cluster_version")
    es_obj = mock_es_repo.return_value
    es_obj.scroll.return_value = scroll_result

    es_obj.MAX_TERMS_SIZE = 1024
    es_obj.meta.prefix = MetaPrefix.AMPERSAND
    es_obj.meta.key = "&key"
    es_obj.meta.id = "&id"
    es_obj.meta.model = "&model"
    es_obj.meta.hash = "&hash"


class TestTranscriptionApiFlow:
    @staticmethod
    def teardown_method():
        if AUDIT_PATH.exists():
            AUDIT_PATH.unlink()

        if os.path.exists(OUTPUT_CSV):
            os.remove(OUTPUT_CSV)

    @requests_mock.Mocker(kw="req_mock")
    @mock_aws
    @freeze_time(time_to_freeze="2023-07-13T13:09:33.430930Z")
    def test_end_to_end_fano_and_deepgram(
        self,
        mocker,
        aries_task_input_deepgram_and_fano: AriesTaskInput,
        tenant_config: addict.Dict,
        static_config_transcription: dict,
        **kwargs,
    ):
        create_and_add_objects_to_s3_bucket(bucket_name="test.dev.steeleye.co")
        kwargs["req_mock"].post(
            "https://deepgram.nonprod-eu-ie-1.steeleye.co/v1/listen?punctuate=true&profanity_filter=false&redact=false&diarize=true&multichannel=false&alternatives=1&numerals=true&utterances=true&detect_language=false&language=id&model=nova-2-general&",
            headers={"Content-Type": "application/json"},
            json=read_json(
                path=DEEPGRAM_API_RESPONSES_PATH.joinpath("indonesian_call.json").as_posix()
            ),
            status_code=HTTPStatus.OK,
        )
        kwargs["req_mock"].post(
            "https://deepgram.nonprod-eu-ie-1.steeleye.co/v1/listen?punctuate=true&profanity_filter=false&redact=false&diarize=true&multichannel=false&alternatives=1&numerals=true&utterances=true&detect_language=false&language=en&model=nova-2-general&",
            headers={"Content-Type": "application/json"},
            json=read_json(
                path=DEEPGRAM_API_RESPONSES_PATH.joinpath("english_call.json").as_posix()
            ),
            status_code=HTTPStatus.OK,
        )
        kwargs["req_mock"].post(
            "https://steeleye-uat.fano.ai/speech/recognize?",
            [
                {
                    "headers": {"Content-Type": "application/json"},
                    "json": read_json(
                        path=FANO_API_RESPONSES_PATH.joinpath("mandarin_call.json").as_posix()
                    ),
                    "status_code": HTTPStatus.OK,
                },
                {
                    "headers": {"Content-Type": "application/json"},
                    "json": read_json(
                        path=FANO_API_RESPONSES_PATH.joinpath("malaysian_call.json").as_posix()
                    ),
                    "status_code": HTTPStatus.OK,
                },
                {
                    "headers": {"Content-Type": "application/json"},
                    "json": read_json(
                        path=FANO_API_RESPONSES_PATH.joinpath(
                            "malaysian_english_mandarin_call.json"
                        ).as_posix()
                    ),
                    "status_code": HTTPStatus.OK,
                },
            ],
        )
        aries_task_result = self._run_aries_task_with_minimal_mocks(
            mocker=mocker,
            sample_aries_task_input=aries_task_input_deepgram_and_fano,
            tenant_config=tenant_config,
            static_config_mock_value=static_config_transcription,
        )
        ndjson_s3_uri: str = aries_task_result.output_param.params["file_uri"]

        local_file_path = run_download_file(file_url=ndjson_s3_uri)
        with open(
            EXPECTED_RESULTS_PATH.joinpath("expected_result_fano_and_deepgram.csv"), "r"
        ) as expected_result:
            expected_result = pd.read_csv(expected_result)

        with open(local_file_path, "r") as flow_result:
            result = pd.read_csv(flow_result)

        os.unlink(local_file_path)

        pd.testing.assert_frame_equal(result.sort_index(axis=1), expected_result.sort_index(axis=1))

        assert aries_task_result.output_param.params == {
            "file_uri": ndjson_s3_uri,
            "stats": {
                "Total records to transcribe": 5,
                "Successful Deepgram transcripts": 2,
                "Successful Fano transcripts": 3,
                TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY: 5,
                TranscriptionStatusEnum.NOT_ATTEMPTED: 0,
                TranscriptionStatusEnum.NOT_COMPLETED_EXCEEDING_CONTRACTUAL_LIMIT: 0,
                TranscriptionStatusEnum.UNSUCCESSFUL: 0,
            },
        }

        # test it marks transcripts as skipped when contractual limit is reached
        with open(AUDIT_PATH.as_posix(), "r") as file:
            audit = json.load(file)
        # Everything went well, nothing to audit
        assert audit == {"input_records": {}, "workflow_status": []}

    @staticmethod
    def _run_aries_task_with_minimal_mocks(
        mocker,
        sample_aries_task_input: AriesTaskInput,
        tenant_config: dict,
        static_config_mock_value: dict = dict(),
    ):
        # Mock Elastic connections across multiple tasks
        mock_es(mocker, module=prepare_for_transcription, scroll_result=pd.DataFrame())
        mock_es(mocker, module=transcriber_task, scroll_result=pd.DataFrame())

        # Mock vault secrets for Fano
        mock_secrets = mocker.patch.object(transcription_api_flow, "get_secrets")
        mock_secrets.return_value = addict.Dict(
            {"fano_bearer_token": "abc", "base_fano_url": "https://steeleye-uat.fano.ai/"}
        )
        mock_secrets_client = mocker.patch.object(transcription_api_flow, "secrets_client")
        mock_secrets_client.return_value = None

        mock_tenant_db = mocker.patch.object(static_config, "TenantAPI")
        mock_tenant_db.return_value.get.return_value = ParsedResponse(
            content={"static_config": static_config_mock_value, "cloud": "aws"},
            raw_response=httpx.Response(status_code=200),
        )

        mock_tenant_config = mocker.patch.object(FetchTenantConfiguration, "_run")
        mock_tenant_config.return_value = tenant_config

        mock_auditor_upload = mocker.patch(
            "integration_wrapper.integration_aries_task.upload_audit"
        )
        mock_auditor_upload.return_value = None

        write_named_temporary_json_mock = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        write_named_temporary_json_mock.side_effect = write_named_temporary_json_side_effect

        mocker.patch.object(
            deepgram_transcriber,
            "amazon_translate",
            return_value="Dummy translation",
        )
        mocker.patch.object(
            fano_transcriber,
            "translate_text",
            return_value="Dummy translation for each segment",
        )

        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": "s3://pinafore.dev.steeleye.co",
                    },
                    "workflow": {"streamed": False},
                },
            ),
        )

        mocker.patch.object(
            target=TranslationConfigAPI,
            attribute="get_config",
            return_value=addict.Dict(
                {
                    "content": {
                        "tenantId": 78,
                        "translationSourceLanguage": None,
                        "workflowId": 109,
                        "translationProvider": "AWS",
                        "updatedDateTime": "2024-07-03T07:10:03.034012",
                        "id": "58284e70-bdfe-4eb3-939c-08ff4e855f41",
                        "translationEnabled": True,
                        "translationTargetLanguage": "ENGLISH",
                        "createdDateTime": "2024-07-03T07:10:03.032582",
                    }
                }
            ),
        )

        # Run flow
        return transcription_api_task.transcription_api_run(
            aries_task_input=sample_aries_task_input
        )

    @freeze_time(time_to_freeze="2023-07-13T13:09:33.430930Z")
    def test_end_to_end_deepgram_only_without_mock_s3(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        tenant_config: dict,
        input_df_deepgram: pd.DataFrame,
    ):
        aries_task_result = self._run_aries_task_with_specific_mock(
            mocker=mocker,
            sample_aries_task_input=sample_aries_task_input,
            tenant_config=tenant_config,
            input_df=input_df_deepgram,
            bucket_name="test.dev.steeleye.co",
        )

        with open(EXPECTED_OUTPUT_CSV_DEEPGRAM_ONLY, "r") as expected_result:
            final_result_expected = pd.read_csv(expected_result)

        with open(OUTPUT_CSV, "r") as flow_result:
            final_result = pd.read_csv(flow_result)

        # note that the source language is only identified for the first two calls
        # because the mocked response has: `detect_language`:True thus, it gets converted to the
        # `en-GB` enum. But as the third call was not transcribed due to the contractual limit, it
        # does not have a source language (as nothing was detected)
        pd.testing.assert_frame_equal(
            final_result.sort_index(axis=1), final_result_expected.sort_index(axis=1)
        )

        assert aries_task_result.output_param.params == {
            "file_uri": OUTPUT_CSV,
            "stats": {
                "Total records to transcribe": 3,
                "Successful Deepgram transcripts": 2,
                "Successful Fano transcripts": 0,
                TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY: 2,
                TranscriptionStatusEnum.NOT_ATTEMPTED: 0,
                TranscriptionStatusEnum.NOT_COMPLETED_EXCEEDING_CONTRACTUAL_LIMIT: 1,
                TranscriptionStatusEnum.UNSUCCESSFUL: 0,
            },
        }

        # test it marks transcripts as skipped when contractual limit is reached
        with open(AUDIT_PATH.as_posix(), "r") as file:
            audit = json.load(file)

        assert audit == {
            "input_records": {
                "file3.wav": {
                    "Transcript": {
                        "created": 0,
                        "errored": 0,
                        "skipped": 1,
                        "duplicate": 0,
                        "updated": 0,
                        "status": [
                            "Transcription could not be completed because the limit was exceeded"
                        ],
                    }
                }
            },
            "workflow_status": [],
        }

    @mock_aws
    @freeze_time(time_to_freeze="2024-03-20 06:59:38.911459+00:00")
    def test_it_can_handle_batches_with_only_recordings_metadata(
        self,
        sample_aries_task_input: AriesTaskInput,
        tenant_config: dict,
        mocker,
    ):
        """This test ensures it can handle batches containing only metadata of
        the recordings."""
        bucket_name: str = "test.dev.steeleye.co"

        # Create mock S3 bucket and objects to it
        create_and_add_objects_to_s3_bucket(bucket_name=bucket_name)

        sample_aries_task_input.input_param.params["file_uri"] = (
            f"s3://{bucket_name}/aries/ingest/masergy_voice/2024/06/07/jdand1e1jdajdaj/elastic_connector/303c5e21c99dc6d3a0dc6d1b4dc22141f237a33f7a64eb053fb7bd2b2be32a0e___raw_success.ndjson"  # noqa: E501
        )

        aries_task_result = self._run_aries_task_no_recordings(
            aries_task_input=sample_aries_task_input,
            bucket_name=bucket_name,
            tenant_config=tenant_config,
            mocker=mocker,
            streamed=True,
        )

        ndjson_s3_uri: str = aries_task_result.output_param.params["file_uri"]

        local_file_path = run_download_file(file_url=ndjson_s3_uri)

        assert aries_task_result.output_param.params == {
            "file_uri": ndjson_s3_uri,
            "stats": {
                "Total records to transcribe": 1,
                "Successful Deepgram transcripts": 0,
                "Successful Fano transcripts": 0,
                TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY: 0,
                TranscriptionStatusEnum.NOT_ATTEMPTED: 1,
                TranscriptionStatusEnum.NOT_COMPLETED_EXCEEDING_CONTRACTUAL_LIMIT: 0,
                TranscriptionStatusEnum.UNSUCCESSFUL: 0,
            },
        }

        pd.testing.assert_frame_equal(
            pd.read_csv(EXPECTED_RESULT_ONLY_RECORDINGS_METADATA.as_posix()),
            pd.read_csv(local_file_path),
        )
        os.unlink(local_file_path)

    @mock_aws
    @freeze_time(time_to_freeze="2024-03-20 06:59:38.911459+00:00")
    def test_transcription_monitored_filtering(
        self,
        sample_aries_task_input: AriesTaskInput,
        tenant_config: dict,
        mocker,
    ):
        """Test that calls are properly filtered based on transcription
        monitoring flag."""
        bucket_name: str = "test.dev.steeleye.co"

        create_and_add_objects_to_s3_bucket(bucket_name=bucket_name)

        sample_aries_task_input.input_param.params["file_uri"] = (
            f"s3://{bucket_name}/aries/ingest/masergy_voice/2024/06/07/jdand1e1jdajdaj/elastic_connector"
            f"/monitoring_test_data.ndjson"
        )

        # Add the restricted-transcription feature flag to tenant configuration
        tenant_config["featureFlags"] = ["restricted-transcription"]

        # Create test data with both monitored and non-monitored participants
        test_input_df = pd.DataFrame(
            [
                {
                    # Monitored call - should be transcribed
                    "&hash": "monitored_hash_1",
                    "&id": "monitored_id_1",
                    "&key": "Call:monitored_id_1",
                    "&model": "Call",
                    "identifiers.fromId": "user1",
                    "voiceFile.fileInfo.location.key": "aries/ingress/depository/"
                    "attachments/masergy_voice/monitored_file1.wav",
                    "callDuration": "00:00:45",
                    "participants": [
                        {
                            "value": {
                                "monitoring": {
                                    "isMonitored": True,
                                    "isMonitoredFor": ["Transcription", "cSurv"],
                                }
                            }
                        }
                    ],
                },
                {
                    # Not monitored call - should be filtered out
                    "&hash": "unmonitored_hash_2",
                    "&id": "unmonitored_id_2",
                    "&key": "Call:unmonitored_id_2",
                    "&model": "Call",
                    "identifiers.fromId": "user2",
                    "voiceFile.fileInfo.location.key": "aries/ingress/depository/"
                    "attachments/masergy_voice/unmonitored_file1.wav",
                    "callDuration": "00:00:45",
                    "participants": [
                        {
                            "value": {
                                "monitoring": {"isMonitored": True, "isMonitoredFor": ["cSurv"]}
                            }
                        }
                    ],
                },
                {
                    # Call with no monitoring data - should be filtered out
                    "&hash": "no_monitoring_hash_3",
                    "&id": "no_monitoring_id_3",
                    "&key": "Call:no_monitoring_id_3",
                    "&model": "Call",
                    "identifiers.fromId": "user3",
                    "voiceFile.fileInfo.location.key": "aries/ingress/depository/"
                    "attachments/masergy_voice/no_monitoring_file1.wav",
                    "callDuration": "00:02:15",
                    "participants": [{"value": {}}],
                },
            ]
        )

        aries_task_result = self._run_aries_task_with_specific_mock(
            sample_aries_task_input=sample_aries_task_input,
            tenant_config=tenant_config,
            mocker=mocker,
            input_df=test_input_df,
            bucket_name=bucket_name,
        )

        ndjson_s3_uri: str = aries_task_result.output_param.params["file_uri"]

        local_file_path = run_download_file(file_url=ndjson_s3_uri)

        assert aries_task_result.output_param.params == {
            "file_uri": ndjson_s3_uri,
            "stats": {
                "Total records to transcribe": 3,
                "Successful Deepgram transcripts": 1,
                "Successful Fano transcripts": 0,
                TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY: 1,
                TranscriptionStatusEnum.NOT_ATTEMPTED: 2,
                TranscriptionStatusEnum.NOT_COMPLETED_EXCEEDING_CONTRACTUAL_LIMIT: 0,
                TranscriptionStatusEnum.UNSUCCESSFUL: 0,
            },
        }

        pd.testing.assert_frame_equal(
            pd.read_csv(EXPECTED_RESULTS_FILTERED_MONITORED_USER.as_posix()),
            pd.read_csv(local_file_path),
        )
        os.unlink(local_file_path)

    @mock_aws
    @freeze_time(time_to_freeze="2024-03-20 06:59:38.911459+00:00")
    def test_when_no_transcription_monitoring_users(
        self,
        sample_aries_task_input: AriesTaskInput,
        tenant_config: dict,
        mocker,
    ):
        """Test that calls are properly filtered based on transcription
        monitoring flag."""
        bucket_name: str = "test.dev.steeleye.co"

        create_and_add_objects_to_s3_bucket(bucket_name=bucket_name)

        sample_aries_task_input.input_param.params["file_uri"] = (
            f"s3://{bucket_name}/aries/ingest/masergy_voice/2024/06/07/jdand1e1jdajdaj/elastic_connector"
            f"/monitoring_test_data.ndjson"
        )

        # Add the restricted-transcription feature flag to tenant configuration
        tenant_config["featureFlags"] = ["restricted-transcription"]

        # Create test data with both monitored and non-monitored participants
        test_input_df = pd.DataFrame(
            [
                {
                    # Not monitored call - should be filtered out
                    "&hash": "unmonitored_hash_2",
                    "&id": "unmonitored_id_2",
                    "&key": "Call:unmonitored_id_2",
                    "&model": "Call",
                    "identifiers.fromId": "user2",
                    "voiceFile.fileInfo.location.key": "aries/ingress/depository/attachments"
                    "/masergy_voice/unmonitored_file1.wav",
                    "callDuration": "00:03:45",
                    "participants": [
                        {
                            "value": {
                                "monitoring": {"isMonitored": True, "isMonitoredFor": ["cSurv"]}
                            }
                        }
                    ],
                },
                {
                    # Call with no monitoring data - should be filtered out
                    "&hash": "no_monitoring_hash_3",
                    "&id": "no_monitoring_id_3",
                    "&key": "Call:no_monitoring_id_3",
                    "&model": "Call",
                    "identifiers.fromId": "user3",
                    "voiceFile.fileInfo.location.key": "aries/ingress/depository/attachments"
                    "/masergy_voice/no_monitoring_file1.wav",
                    "callDuration": "00:02:15",
                    "participants": [{"value": {}}],
                },
            ]
        )

        mock_ndjson_to_df = mocker.patch.object(transcription_api_flow, "ndjson_to_flat_dataframe")
        mock_ndjson_to_df.return_value = test_input_df

        aries_task_result = self._run_aries_task_with_specific_mock(
            sample_aries_task_input=sample_aries_task_input,
            tenant_config=tenant_config,
            mocker=mocker,
            input_df=test_input_df,
            bucket_name=bucket_name,
        )

        ndjson_s3_uri: str = aries_task_result.output_param.params["file_uri"]

        local_file_path = run_download_file(file_url=ndjson_s3_uri)

        assert aries_task_result.output_param.params == {
            "file_uri": ndjson_s3_uri,
            "stats": {
                "Total records to transcribe": 2,
                "Successful Deepgram transcripts": 0,
                "Successful Fano transcripts": 0,
                TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY: 0,
                TranscriptionStatusEnum.NOT_ATTEMPTED: 2,
                TranscriptionStatusEnum.NOT_COMPLETED_EXCEEDING_CONTRACTUAL_LIMIT: 0,
                TranscriptionStatusEnum.UNSUCCESSFUL: 0,
            },
        }

        pd.testing.assert_frame_equal(
            pd.read_csv(EXPECTED_RESULTS_NO_MONITORED_USER.as_posix()),
            pd.read_csv(local_file_path),
        )
        os.unlink(local_file_path)

    @mock_aws
    @freeze_time(time_to_freeze="2024-03-20 06:59:38.911459+00:00")
    def test_override_for_skip_transcription_by_duration(
        self,
        sample_aries_task_input_arrow_override: AriesTaskInput,
        tenant_config: dict,
        mocker,
        bucket_name: str = "arrow.dev.steeleye.co",
    ):
        """Test that calls are properly filtered based on duration."""
        create_and_add_objects_to_s3_bucket(bucket_name=bucket_name)
        sample_aries_task_input_arrow_override.input_param.params["file_uri"] = (
            f"s3://{bucket_name}/aries/ingest/masergy_voice/2024/06/07/jdand1e1jdajdaj/elastic_connector"
            f"/skip_by_duration_test_data.ndjson"
        )
        test_input_df = pd.DataFrame(
            [
                {
                    "&hash": "invalid_duration_hash_2",
                    "&id": "invalid_duration_id_2",
                    "&key": "Call:invalid_duration_id_2",
                    "&model": "Call",
                    "identifiers.fromId": "user2",
                    "voiceFile.fileInfo.location.key": "aries/ingress/depository/attachments"
                    "/masergy_voice/invalid_duration_file1.wav",
                    "callDuration": "00:00:09",
                    "participants": [{"value": {}}],
                },
                {
                    "&hash": "valid_duration_hash_3",
                    "&id": "valid_duration_id_3",
                    "&key": "Call:valid_duration_id_3",
                    "&model": "Call",
                    "identifiers.fromId": "user3",
                    "voiceFile.fileInfo.location.key": "aries/ingress/depository/attachments"
                    "/masergy_voice/valid_duration_file1.wav",
                    "callDuration": "00:02:15",
                    "participants": [{"value": {}}],
                },
                {
                    "&hash": "valid_duration_hash_4",
                    "&id": "valid_duration_id_4",
                    "&key": "Call:valid_duration_id_4",
                    "&model": "Call",
                    "identifiers.fromId": "user3",
                    "voiceFile.fileInfo.location.key": "aries/ingress/depository/attachments"
                    "/masergy_voice/valid_duration_file4.wav",
                    "callDuration": "00:12:15",
                    "participants": [{"value": {}}],
                },
                {
                    # Call with no monitoring data - should be filtered out
                    "&hash": "invalid_duration_hash_3",
                    "&id": "invalid_duration_id_3",
                    "&key": "Call:invalid_duration_id_3",
                    "&model": "Call",
                    "identifiers.fromId": "user3",
                    "voiceFile.fileInfo.location.key": "aries/ingress/depository/attachments"
                    "/masergy_voice/invalid_duration_file1.wav",
                    "callDuration": "00:20:15",
                    "participants": [{"value": {}}],
                },
            ]
        )
        mock_ndjson_to_df = mocker.patch.object(transcription_api_flow, "ndjson_to_flat_dataframe")
        mock_ndjson_to_df.return_value = test_input_df
        aries_task_result = self._run_aries_task_with_specific_mock(
            sample_aries_task_input=sample_aries_task_input_arrow_override,
            tenant_config=tenant_config,
            input_df=test_input_df,
            mocker=mocker,
            bucket_name=bucket_name,
        )
        ndjson_s3_uri: str = aries_task_result.output_param.params["file_uri"]

        local_file_path = run_download_file(file_url=ndjson_s3_uri)

        assert aries_task_result.output_param.params == {
            "file_uri": ndjson_s3_uri,
            "stats": {
                "Total records to transcribe": 4,
                "Successful Deepgram transcripts": 2,
                "Successful Fano transcripts": 0,
                TranscriptionStatusEnum.COMPLETED_SUCCESSFULLY: 2,
                TranscriptionStatusEnum.NOT_ATTEMPTED: 2,
                TranscriptionStatusEnum.NOT_COMPLETED_EXCEEDING_CONTRACTUAL_LIMIT: 0,
                TranscriptionStatusEnum.UNSUCCESSFUL: 0,
            },
        }
        pd.testing.assert_frame_equal(
            pd.read_csv(EXPECTED_RESULTS_FILTERED_BY_DURATION.as_posix()),
            pd.read_csv(local_file_path),
        )
        os.unlink(local_file_path)

    @staticmethod
    def _run_aries_task_no_recordings(
        tenant_config: dict,
        aries_task_input: AriesTaskInput,
        bucket_name: str,
        streamed: bool,
        mocker,
        static_config_mock_value: dict = dict(),
    ):
        """Generic task runner."""
        # Mock Elastic connections across multiple tasks
        mock_es(mocker, module=prepare_for_transcription, scroll_result=pd.DataFrame())
        mock_es(mocker, module=transcriber_task, scroll_result=pd.DataFrame())

        mock_tenant_config = mocker.patch.object(FetchTenantConfiguration, "_run")
        mock_tenant_config.return_value = tenant_config

        mock_tenant_db = mocker.patch.object(static_config, "TenantAPI")
        mock_tenant_db.return_value.get.return_value = ParsedResponse(
            content={"static_config": static_config_mock_value, "cloud": "aws"},
            raw_response=httpx.Response(status_code=200),
        )

        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": f"s3://{bucket_name}",
                        "realm": bucket_name,
                    },
                    "workflow": {"streamed": streamed},
                },
            ),
        )

        mocker.patch.object(
            target=TranslationConfigAPI,
            attribute="get_config",
            return_value=addict.Dict({}),
        )

        return transcription_api_task.transcription_api_run(aries_task_input=aries_task_input)

    @staticmethod
    def _run_aries_task_with_specific_mock(
        mocker,
        sample_aries_task_input: AriesTaskInput,
        tenant_config: dict,
        input_df: pd.DataFrame,
        bucket_name: str,
        static_config_mock_value: dict = dict(),
    ):
        # Mock Elastic connections across multiple tasks
        mock_es(mocker, module=prepare_for_transcription, scroll_result=pd.DataFrame())
        mock_es(mocker, module=transcriber_task, scroll_result=pd.DataFrame())

        mock_tenant_config = mocker.patch.object(FetchTenantConfiguration, "_run")
        mock_tenant_config.return_value = tenant_config

        mock_ndjson_to_df = mocker.patch.object(transcription_api_flow, "ndjson_to_flat_dataframe")
        mock_ndjson_to_df.return_value = input_df

        mock_tenant_db = mocker.patch.object(static_config, "TenantAPI")
        mock_tenant_db.return_value.get.return_value = ParsedResponse(
            content={"static_config": static_config_mock_value, "cloud": "aws"},
            raw_response=httpx.Response(status_code=200),
        )

        mocker.patch.object(
            transcriber_task,
            "is_file_in_cloud",
            side_effect=[
                False,
                False,
                False,
                False,
                False,
            ],
        )

        mock_auditor_upload = mocker.patch(
            "integration_wrapper.integration_aries_task.upload_audit"
        )
        mock_auditor_upload.return_value = None

        write_named_temporary_json_mock = mocker.patch(
            "integration_wrapper.integration_aries_task.write_named_temporary_json"
        )
        write_named_temporary_json_mock.side_effect = write_named_temporary_json_side_effect

        transcription_response = {
            "metadata": {
                "request_id": "testID",
                "model_info": {
                    "4899aa60-f723-4517-9815-2042acc12a82": {
                        "name": "general",
                        "version": "2022-01-18.0",
                        "tier": "base",
                    }
                },
                "created": "2022-07-19T13:55:02.965Z",
            },
            "results": {
                "channels": [
                    {
                        "alternatives": [
                            {
                                "transcript": "Hello",
                                "confidence": 0.9978787,
                            }
                        ],
                        "detected_language": "en",
                    }
                ]
            },
        }
        # MagicMock with a .json() and ok=True
        mock_response = MagicMock()
        mock_response.ok = True
        mock_response.json.return_value = transcription_response

        # Patch the transcribe method to return this mock response
        mock_transcription_response = mocker.patch.object(
            deepgram_transcriber.DeepgramTranscriber,
            "transcribe_with_api",
        )
        mock_transcription_response.return_value = mock_response

        mocker.patch.object(
            deepgram_transcriber,
            "amazon_translate",
            return_value="Bonjour",
        )
        mocker.patch.object(
            transcriber_task,
            "md5_encode",
            side_effect=[
                "file3_hash",
                "file3_hash",
                "file4_hash",
                "file4_hash",
                "file5_hash",
                "file5_hash",
            ],
        )
        mocker.patch.object(
            transcriber_task,
            "write_json",
            return_value=None,
        )
        mocker.patch.object(
            transcriber_task,
            "get_transcription_usage",
            return_value={
                VendorEnum.AWS.value: 0,
                VendorEnum.DEEP_GRAM.value: 0,
                VendorEnum.FANO.value: 0,
                VendorEnum.INTELLIGENT_VOICE.value: 0,
                VendorEnum.SPEECHMATICS.value: 0,
                VendorEnum.VERINT.value: 0,
            },
        )

        mock_datetime = mocker.patch.object(
            deepgram_transcriber.DeepgramTranscriber,
            "get_transcription_datetime_start",
        )
        mock_datetime.return_value = "2022-07-19T13:55:02.965Z"

        determine_target_path_csv = mocker.patch.object(
            CreateTranscriptionFeedInput,
            "determine_target_path",
        )
        determine_target_path_csv.return_value = OUTPUT_CSV

        mocker.patch.object(
            target=CachedTenantWorkflowAPIClient,
            attribute="get",
            return_value=addict.Dict(
                {
                    "tenant": {
                        "lake_prefix": f"s3://{bucket_name}",
                    },
                    "workflow": {"streamed": False},
                },
            ),
        )

        mocker.patch.object(
            target=TranslationConfigAPI,
            attribute="get_config",
            return_value=addict.Dict(
                {
                    "content": {
                        "tenantId": 78,
                        "translationSourceLanguage": None,
                        "workflowId": 109,
                        "translationProvider": "AWS",
                        "updatedDateTime": "2024-07-03T07:10:03.034012",
                        "id": "58284e70-bdfe-4eb3-939c-08ff4e855f41",
                        "translationEnabled": None,
                        "translationTargetLanguage": "ENGLISH",
                        "createdDateTime": "2024-07-03T07:10:03.032582",
                    }
                }
            ),
        )

        # Run flow
        return transcription_api_task.transcription_api_run(
            aries_task_input=sample_aries_task_input
        )


def write_named_temporary_json_side_effect(output_filename: str, **kwargs) -> str:
    if output_filename == "audit.json":
        with fsspec.open(AUDIT_PATH.as_posix(), "w") as file:
            json.dump({}, file)

        return AUDIT_PATH.as_posix()

    return write_named_temporary_json(output_filename=output_filename, **kwargs)


def create_and_add_objects_to_s3_bucket(bucket_name: str):
    """Recreate the s3 bucket, and copy all the files from `LOCAL_BUCKET_PATH`
    to s3 mocked bucket.

    :param bucket_name: Bucket name of Mock S3 bucket
    :return: None
    """

    # Create bucket
    s3 = boto3.client("s3", region_name="us-east-1")
    s3.create_bucket(Bucket=bucket_name)

    bucket_path = LOCAL_BUCKETS_PATH.joinpath(bucket_name)

    for file_ in bucket_path.rglob("*"):
        if file_.is_file():
            _path = file_.as_posix().replace(f"{bucket_path}/", "")

            with open(file_, "rb") as f:
                s3.put_object(Bucket=bucket_name, Key=_path, Body=f.read())
