# type: ignore
# flake8: noqa: E402
import copy
import fsspec
import httpx
import json
import os
import se_schema_meta
from aries_se_api_client.base import ParsedResponse
from freezegun import freeze_time
from integration_wrapper.static import IntegrationAriesTaskVariables
from se_elastic_schema.static.tenant_configuration import FeatureFlags

# This needs to be here since the conftest is loaded first
# than any import on the test files
os.environ[IntegrationAriesTaskVariables.DATA_PLATFORM_CONFIG_API_URL] = (
    "https://test-enterprise.steeleye.co"
)
os.environ["STACK"] = "dev-shared-2"
import pandas as pd
import pytest
import tempfile
from addict import addict
from aries_config_api_compatible_client.tenant_workflow import CompatibleTenantWorkflowAPIClient
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_task_link.models import AriesTaskInput
from copilot_utils.static import OpenAIResponse
from datetime import datetime
from integration_wrapper.static import IntegrationAriesTaskVariables
from openai.types import CompletionUsage
from openai.types.chat import ChatCompletion, ChatCompletionMessage
from openai.types.chat.chat_completion import Choice
from pathlib import Path
from se_elasticsearch.repository.models import ResourceConfig
from se_elasticsearch.repository.static import MetaPrefix
from se_enums.elastic_search import EsActionEnum
from shutil import rmtree
from typing import Any, Optional
from unittest import mock
from unittest.mock import patch

CURRENT_PATH = Path(__file__).parent
DATA_PATH = CURRENT_PATH.joinpath("data")
INPUT_NDJSON_TRANSCRIPT = (
    f"{CURRENT_PATH}/data/valid_executions/scenario_1/sample_transcript.ndjson"
)
INPUT_NDJSON_CALL = f"{CURRENT_PATH}/data/valid_executions/scenario_1/sample_call.ndjson"
TEMP_FILE_TRANSCRIPT = tempfile.NamedTemporaryFile(suffix="__transcript__.ndjson")
TEMP_FILE_CALL = tempfile.NamedTemporaryFile(suffix="__call__.ndjson")

# Create separate temp files for the ES batch scenario to avoid conflicts
TEMP_FILE_TRANSCRIPT_ES = tempfile.NamedTemporaryFile(suffix="__transcript_es_batch_1__.ndjson")
TEMP_FILE_CALL_ES = tempfile.NamedTemporaryFile(suffix="__call_es_batch_1__.ndjson")
# Add temp files for the second batch
TEMP_FILE_TRANSCRIPT_ES_2 = tempfile.NamedTemporaryFile(suffix="__transcript_es_batch_2__.ndjson")
TEMP_FILE_CALL_ES_2 = tempfile.NamedTemporaryFile(suffix="__call_es_batch_2__.ndjson")


EXPECTED_TRANSCRIPT_OUTPUT_NDJSON = (
    f"{CURRENT_PATH}/data/valid_executions/scenario_1/expected_output_transcript.ndjson"
)
EXPECTED_CALL_OUTPUT_NDJSON = (
    f"{CURRENT_PATH}/data/valid_executions/scenario_1/expected_output_call.ndjson"
)
SERIALIZER_TMP_DIR = CURRENT_PATH.joinpath("serializer_tmp_dir")
SERIALIZER_TMP_DIR.mkdir(parents=True, exist_ok=True)
DEFAULT_OPENAI_VALUES = {
    "OPENAI_API_BASE": "",
    "OPENAI_API_KEY": "",
    "OPENAI_API_MODEL": "",
    "OPENAI_API_VERSION": "2023-03-15-preview",
    "PERFORM_OUTPUT_CACHE_RESOLUTION": "False",
    "PERFORM_LLM_IO_AUDIT": "False",
}
MOCK_RESPONSE = {
    "tenant": {
        "lake_prefix": "s3://pinafore.dev.steeleye.co/",
        "stack": "dev-shared-2",
        "static_config": {"copilot_duration_threshold": 10},
    },
}

os.environ[IntegrationAriesTaskVariables.SERIALIZER_TMP_DIR] = SERIALIZER_TMP_DIR.as_posix()


@pytest.fixture(scope="session", autouse=True)
def cleanup(request):
    def _end():
        rmtree(SERIALIZER_TMP_DIR)
        CURRENT_PATH.joinpath("result.json").unlink(missing_ok=True)
        CURRENT_PATH.joinpath("batch_0.csv").unlink(missing_ok=True)

    request.addfinalizer(_end)


def mock_es(mocker, module, scroll_result: pd.DataFrame):
    # Mocks for ElasticSearch
    mock_get_es_config = mocker.patch.object(module, "get_es_config")
    mock_get_es_config.return_value = ResourceConfig(
        host="localhost",
        port="9200",
        scheme="http",
        meta_prefix=MetaPrefix.AMPERSAND,
    )

    mock_es_repo = mocker.patch.object(module, "get_repository_by_cluster_version")
    es_obj = mock_es_repo.return_value
    es_obj.scroll.return_value = scroll_result

    es_obj.MAX_TERMS_SIZE = 1024
    es_obj.meta.prefix = MetaPrefix.AMPERSAND
    es_obj.meta.key = "&key"
    es_obj.meta.id = "&id"
    es_obj.meta.model = "&model"
    es_obj.meta.hash = "&hash"


@pytest.fixture()
def sample_aries_task_input() -> AriesTaskInput:
    workflow = WorkflowFieldSet(
        trace_id="test_transcription_copilot_1",
        name="cloud9_voice",
        stack="dev-shared-2",
        tenant="irises8",
        start_timestamp=datetime.now(),
    )
    input_param = IOParamFieldSet(
        params={
            "Transcript": {
                "params": {
                    "file_uri": "s3://irises8.dev.steeleye.co/aries/ingest/sample_transcript"
                    ".ndjson",
                    "es_action": "create",
                    "data_model": "se_elastic_schema.models.tenant.communication.transcript:Transcript",  # noqa: E501
                }
            },
            "Call": {
                "params": {
                    "file_uri": "s3://irises8.dev.steeleye.co/aries/ingest/sample_call.ndjson",
                    "es_action": "update",
                    "data_model": "se_elastic_schema.models.tenant.communication.call:Call",
                }
            },
            "tenant_config_feature_flags": [FeatureFlags.CALL_TRANSCRIPTION_COPILOT],
        }
    )
    task = TaskFieldSet(name="test_transcription_copilot", version="latest", success=False)
    return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)


@pytest.fixture()
def mock_gpt_response():
    with open(DATA_PATH.joinpath("valid_executions/scenario_1/mock_completion_1.json")) as f:
        content_1 = json.dumps(json.load(f))
    with open(DATA_PATH.joinpath("valid_executions/scenario_1/mock_completion_2.json")) as f:
        content_2 = json.dumps(json.load(f))
    with open(DATA_PATH.joinpath("valid_executions/scenario_1/mock_completion_3.json")) as f:
        content_3 = json.dumps(json.load(f))

    openai_response_template_1 = ChatCompletion(
        id="abc",
        created=1727699717,
        model="sample_model",
        object="chat.completion",
        usage=CompletionUsage(
            total_tokens=100,
            # Leaving these as zero as they are not relevant to the tests
            prompt_tokens=0,
            completion_tokens=0,
        ),
        choices=[
            Choice(
                message=ChatCompletionMessage(content=content_1, role="assistant"),
                index=0,
                finish_reason="stop",
            )
        ],
    )

    openai_response_template_2 = copy.deepcopy(openai_response_template_1)
    openai_response_template_2.choices[0].message.content = content_2
    openai_response_template_3 = copy.deepcopy(openai_response_template_1)
    openai_response_template_3.choices[0].message.content = content_3
    return [
        OpenAIResponse(index=1, response=openai_response_template_1),
        OpenAIResponse(index=2, response=openai_response_template_2),
        OpenAIResponse(index=3, response=openai_response_template_3),
    ]


@pytest.fixture()
def mock_empty_gpt_response():
    """Mock GPT response with empty/null values for all required fields"""
    empty_content = json.dumps(
        {
            "transcriptionSummary": None,
            "entities": [],
            "topics": [],
            "sentiments": [],
            "classifier": None,
            "questions": [],
            "complexity": {"score": None},  # Object with null value
            "risks": [],
        }
    )

    openai_response_template = ChatCompletion(
        id="abc",
        created=1727699717,
        model="sample_model",
        object="chat.completion",
        usage=CompletionUsage(
            total_tokens=100,
            prompt_tokens=0,
            completion_tokens=0,
        ),
        choices=[
            Choice(
                message=ChatCompletionMessage(content=empty_content, role="assistant"),
                index=0,
                finish_reason="stop",
            )
        ],
    )
    return [
        OpenAIResponse(index=1, response=openai_response_template),
        OpenAIResponse(index=2, response=openai_response_template),
    ]


@pytest.fixture()
def mock_output_destination_fixture(mocker):
    """Fixture that provides mock for create_ndjson_path"""

    def _mock(transcription_copilot_flow):
        return mocker.patch.object(transcription_copilot_flow, "create_ndjson_path")

    return _mock


class TestTranscriptionCopilotFlow:
    @pytest.mark.parametrize(
        "config_db_param",
        [
            {
                "static_config": {
                    "transcription_copilot_duration_threshold": 10,
                    "random_parameter_to_test_ignore": "foo",
                },
                "tenant": {
                    "lake_prefix": "s3://pinafore.dev.steeleye.co",
                    "stack": "dev-shared-2",
                },
                "workflow": {"streamed": False},
            },
            {
                "tenant": {
                    "lake_prefix": "s3://pinafore.dev.steeleye.co",
                    "stack": "dev-shared-2",
                },
                "workflow": {"streamed": False},
            },
        ],
    )
    @mock.patch.dict(os.environ, DEFAULT_OPENAI_VALUES)
    def test_valid_executions(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        mock_gpt_response,
        mock_output_destination_fixture,
        config_db_param,
        caplog,
    ):
        from integration_audio_comms_tasks.transcription.transcription_copilot import (
            transcription_copilot_flow,
        )

        mock_output_destination = mock_output_destination_fixture(transcription_copilot_flow)
        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=sample_aries_task_input,
            mock_gpt_response=mock_gpt_response,
            config_db_param=config_db_param,
            mock_output_destination_fixture=mock_output_destination,
        )

        expected_transcript_df = pd.read_json(EXPECTED_TRANSCRIPT_OUTPUT_NDJSON, lines=True)
        expected_call_df = pd.read_json(EXPECTED_CALL_OUTPUT_NDJSON, lines=True)
        final_transcript_df = pd.read_json(TEMP_FILE_TRANSCRIPT.name, lines=True)
        final_call_df = pd.read_json(TEMP_FILE_CALL.name, lines=True)
        pd.testing.assert_frame_equal(
            final_call_df.sort_index(axis=1),
            expected_call_df.sort_index(axis=1),
        )
        pd.testing.assert_frame_equal(
            final_transcript_df.sort_index(axis=1),
            expected_transcript_df.sort_index(axis=1),
        )
        assert aries_task_result.output_param.params["Transcript"]["params"] == {
            "file_uri": TEMP_FILE_TRANSCRIPT.name,
            "es_action": EsActionEnum.INDEX.value,
            "data_model": "se_elastic_schema.models.tenant.communication.transcript:Transcript",
            "ignore_empty_file_uri": True,
        }
        assert aries_task_result.output_param.params["Call"]["params"] == {
            "file_uri": TEMP_FILE_CALL.name,
            "es_action": EsActionEnum.UPDATE.value,
            "data_model": "se_elastic_schema.models.tenant.communication.call:Call",
            "ignore_empty_file_uri": True,
        }
        assert "spoken_neural_watches_list" not in aries_task_result.output_param.params.keys()
        assert "upper_bound_date" not in aries_task_result.output_param.params.keys()
        if "static_config" not in config_db_param:
            assert (
                "static_config not found in tenant workflow config. "
                "Using default transcription copilot settings." in caplog.text
            )

    @mock.patch.dict(os.environ, DEFAULT_OPENAI_VALUES)
    def test_skip_in_self_hosted_scenario(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        mock_gpt_response,
        mock_output_destination_fixture,
    ):
        task_input = copy.deepcopy(sample_aries_task_input)
        task_input.input_param.params["tenant_config_feature_flags"].append(
            FeatureFlags.SELF_HOSTED_LLM
        )

        from integration_audio_comms_tasks.transcription.transcription_copilot import (
            transcription_copilot_flow,
        )

        mock_output_destination = mock_output_destination_fixture(transcription_copilot_flow)

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=task_input,
            mock_gpt_response=mock_gpt_response,
            mock_output_destination_fixture=mock_output_destination,
            config_db_param={
                "workflow_last_executed": "2025-07-15T00:05:03+00:00",
                "tenant": {
                    "lake_prefix": "s3://pinafore.dev.steeleye.co",
                },
            },
        )

        assert aries_task_result.output_param.params["Transcript"]["params"] == {
            "file_uri": task_input.input_param.params["Transcript"]["params"]["file_uri"],
            "es_action": EsActionEnum.CREATE.value,
            "data_model": "se_elastic_schema.models.tenant.communication.transcript:Transcript",
            "ignore_empty_file_uri": True,
        }
        assert aries_task_result.output_param.params["Call"]["params"] == {
            "file_uri": task_input.input_param.params["Call"]["params"]["file_uri"],
            "es_action": EsActionEnum.UPDATE.value,
            "data_model": "se_elastic_schema.models.tenant.communication.call:Call",
            "ignore_empty_file_uri": True,
        }

    @freeze_time("2025-07-15 00:06:03")
    @mock.patch.dict(os.environ, DEFAULT_OPENAI_VALUES)
    def test_valid_executions_elasticsearch(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        mock_gpt_response: list[OpenAIResponse],
        mock_output_destination_fixture,
    ):
        """Tests the flow when data is fetched from
        Elasticsearch and processed in multiple batches."""
        from integration_audio_comms_tasks.transcription.transcription_copilot import (
            transcription_copilot_flow,
        )

        task_input = copy.deepcopy(sample_aries_task_input)
        del task_input.input_param.params["Transcript"]["params"]["file_uri"]
        del task_input.input_param.params["Call"]["params"]["file_uri"]

        es_output_files = [
            TEMP_FILE_TRANSCRIPT_ES.name,  # Batch 1 transcript
            TEMP_FILE_CALL_ES.name,  # Batch 1 call
            TEMP_FILE_TRANSCRIPT_ES_2.name,  # Batch 2 transcript
            TEMP_FILE_CALL_ES_2.name,  # Batch 2 call
        ]

        mock_watches = [{se_schema_meta.ID: "watch1"}, {se_schema_meta.ID: "watch2"}]

        mock_output_destination = mock_output_destination_fixture(transcription_copilot_flow)

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=task_input,
            mock_gpt_response=mock_gpt_response,
            elasticsearch_mode=True,
            es_output_files=es_output_files,
            completion_side_effect=[mock_gpt_response] * 2,
            mock_spoken_neural_watches=mock_watches,
            mock_output_destination_fixture=mock_output_destination,
            config_db_param={
                "workflow_last_executed": "2025-07-15T00:05:03+00:00",
                "tenant": {
                    "lake_prefix": "s3://pinafore.dev.steeleye.co",
                },
            },
        )

        # Load all expected and actual data
        expected_transcript_df = pd.read_json(EXPECTED_TRANSCRIPT_OUTPUT_NDJSON, lines=True)
        expected_call_df = pd.read_json(EXPECTED_CALL_OUTPUT_NDJSON, lines=True)

        # Batch 1 results
        final_transcript_df_1 = pd.read_json(TEMP_FILE_TRANSCRIPT_ES.name, lines=True)
        final_call_df_1 = pd.read_json(TEMP_FILE_CALL_ES.name, lines=True)

        # Batch 2 results
        final_transcript_df_2 = pd.read_json(TEMP_FILE_TRANSCRIPT_ES_2.name, lines=True)
        final_call_df_2 = pd.read_json(TEMP_FILE_CALL_ES_2.name, lines=True)

        # Assert data for each batch is correct
        pd.testing.assert_frame_equal(
            final_call_df_1.sort_index(axis=1),
            expected_call_df.sort_index(axis=1),
        )
        pd.testing.assert_frame_equal(
            final_transcript_df_1.sort_index(axis=1),
            expected_transcript_df.sort_index(axis=1),
        )
        pd.testing.assert_frame_equal(
            final_call_df_2.sort_index(axis=1),
            expected_call_df.sort_index(axis=1),
        )
        pd.testing.assert_frame_equal(
            final_transcript_df_2.sort_index(axis=1),
            expected_transcript_df.sort_index(axis=1),
        )

        # Check the structure of the AriesTaskResult for the batch flow
        result = aries_task_result.output_param.params["io_params_list"]

        assert len(result) == 2

        # Assert batch 1 params
        assert result[0]["Transcript"]["params"]["file_uri"] == TEMP_FILE_TRANSCRIPT_ES.name
        assert result[0]["Call"]["params"]["file_uri"] == TEMP_FILE_CALL_ES.name

        # Assert batch 2 params
        assert result[1]["Transcript"]["params"]["file_uri"] == TEMP_FILE_TRANSCRIPT_ES_2.name
        assert result[1]["Call"]["params"]["file_uri"] == TEMP_FILE_CALL_ES_2.name

        # Assert that create_ndjson_path was called 4 times (2 batches * 2 files each)
        assert mock_output_destination.call_count == 4

        # Assert that the watch details are present in the output
        assert aries_task_result.output_param.params["spoken_neural_watches_list"] == [
            dict(io_param=dict(params=dict(watch_id=watch[se_schema_meta.ID])))
            for watch in mock_watches
        ]

        # Assert that the last execution details are present in the output
        assert (
            aries_task_result.output_param.params["upper_bound_date"] == "2025-07-15T00:06:03+00:00"
        )

    @mock.patch.dict(os.environ, DEFAULT_OPENAI_VALUES)
    def test_skip_empty_required_columns_elasticsearch(
        self,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        mock_empty_gpt_response: list[OpenAIResponse],
        mock_output_destination_fixture,
    ):
        """Tests that batches with empty required columns are skipped in ES-based flow"""
        from integration_audio_comms_tasks.transcription.transcription_copilot import (
            transcription_copilot_flow,
        )

        task_input = copy.deepcopy(sample_aries_task_input)
        del task_input.input_param.params["Transcript"]["params"]["file_uri"]
        del task_input.input_param.params["Call"]["params"]["file_uri"]
        task_input.input_param.params["start_date_time"] = "2025-06-02T16:03:57"
        task_input.input_param.params["end_date_time"] = "2025-06-02T16:04:57"

        mock_watches = [{se_schema_meta.ID: "watch1"}, {se_schema_meta.ID: "watch2"}]

        mock_output_destination = mock_output_destination_fixture(transcription_copilot_flow)

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=task_input,
            mock_gpt_response=mock_empty_gpt_response,
            elasticsearch_mode=True,
            es_output_files=[],  # No output files expected since batches are skipped
            completion_side_effect=[mock_empty_gpt_response] * 2,
            mock_spoken_neural_watches=mock_watches,
            mock_output_destination_fixture=mock_output_destination,
            config_db_param={
                "workflow_last_executed": "2025-07-15T00:05:03+00:00",
                "tenant": {
                    "lake_prefix": "s3://pinafore.dev.steeleye.co",
                },
            },
        )

        # Assert that no output files were created since all batches had empty required columns
        mock_output_destination.assert_not_called()

        # Check that the result contains empty io_params_list
        result = aries_task_result.output_param.params["io_params_list"]
        assert len(result) == 0

        # Assert that the watch details are present in the output
        assert aries_task_result.output_param.params["spoken_neural_watches_list"] == [
            dict(io_param=dict(params=dict(watch_id=watch[se_schema_meta.ID])))
            for watch in mock_watches
        ]

        # Assert that the last execution details are present in the output
        assert (
            aries_task_result.output_param.params["upper_bound_date"] == "2025-07-15T00:05:03+00:00"
        )

    @mock.patch.dict(os.environ, DEFAULT_OPENAI_VALUES)
    @patch("aries_se_core_tasks.static_config.static_config.TenantWorkflowAPI")
    def test_mixed_empty_and_valid_batches_elasticsearch(
        self,
        mock_tenant_client,
        mocker,
        sample_aries_task_input: AriesTaskInput,
        mock_gpt_response: list[OpenAIResponse],
        mock_empty_gpt_response: list[OpenAIResponse],
        mock_output_destination_fixture,
    ):
        """Tests mixed scenario: some batches have empty required columns, some don't"""
        from integration_audio_comms_tasks.transcription.transcription_copilot import (
            transcription_copilot_flow,
        )
        from aries_se_core_tasks.static_config.static_config import (
            ConfigApiTable,
            get_static_config,
        )
        from integration_audio_comms_tasks.transcription.transcription_copilot.input_schema import (
            TranscriptionCopilotStaticConfig,
        )
        mock_response = ParsedResponse(
            content={"static_config": {"feature_a": True, "some_setting": "custom"}},
            raw_response=httpx.Response(status_code=200),
        )
        mock_tenant_client.return_value.get.return_value = mock_response

        # config = get_static_config(
        #     table=ConfigApiTable.TENANT_WORKFLOW,
        #     model=TranscriptionCopilotStaticConfig,
        #     tenant_name="irises8",
        #     stack_name="dev-shared-2",
        #     workflow_name="test_transcription_copilot",
        # )

        task_input = copy.deepcopy(sample_aries_task_input)
        del task_input.input_param.params["Transcript"]["params"]["file_uri"]
        del task_input.input_param.params["Call"]["params"]["file_uri"]

        es_output_files = [
            TEMP_FILE_TRANSCRIPT_ES_2.name,  # Only for valid batch
            TEMP_FILE_CALL_ES_2.name,
        ]

        mock_watches = [{se_schema_meta.ID: "watch1"}, {se_schema_meta.ID: "watch2"}]

        mock_output_destination = mock_output_destination_fixture(transcription_copilot_flow)

        aries_task_result = self._run_aries_task(
            mocker=mocker,
            sample_aries_task_input=task_input,
            mock_gpt_response=mock_gpt_response,
            elasticsearch_mode=True,
            es_output_files=es_output_files,
            completion_side_effect=[mock_empty_gpt_response, mock_gpt_response],
            mock_spoken_neural_watches=mock_watches,
            mock_output_destination_fixture=mock_output_destination,
            config_db_param={
                "workflow_last_executed": "2025-07-15T00:05:03+00:00",
                "tenant": {
                    "lake_prefix": "s3://pinafore.dev.steeleye.co",
                },
            },
        )

        # Assert that output files were created only for valid batch (2 calls for 1 batch)
        assert mock_output_destination.call_count == 2

        # Check that the result contains only one batch (the valid one)
        result = aries_task_result.output_param.params["io_params_list"]
        assert len(result) == 1

        # Verify the valid batch output
        assert result[0]["Transcript"]["params"]["file_uri"] == TEMP_FILE_TRANSCRIPT_ES_2.name
        assert result[0]["Call"]["params"]["file_uri"] == TEMP_FILE_CALL_ES_2.name

        # Assert that the watch details are present in the output
        assert aries_task_result.output_param.params["spoken_neural_watches_list"] == [
            dict(io_param=dict(params=dict(watch_id=watch[se_schema_meta.ID])))
            for watch in mock_watches
        ]

    @staticmethod
    def _run_aries_task(
        mocker,
        sample_aries_task_input: AriesTaskInput,
        mock_gpt_response: list[OpenAIResponse],
        mock_output_destination_fixture: mock.MagicMock,
        es_output_files: list[str] | None = None,
        elasticsearch_mode: bool = False,
        completion_side_effect: list[Any] | None = None,
        mock_spoken_neural_watches: list[dict] | None = None,
        config_db_param: Optional[dict] = None,
    ):
        from aries_config_api_compatible_client.tenant_workflow import (
            CompatibleTenantWorkflowAPIClient,
        )
        from aries_se_core_tasks.io.read.fetch_tenant_configuration import FetchTenantConfiguration
        from copilot_utils.client import SeOpenApiClient
        from integration_audio_comms_tasks.transcription.transcription_copilot import (
            transcription_copilot_flow,
            transcription_copilot_task,
        )

        # Mock cached tenant config
        mock_tenant_workflow_config_get = mocker.patch.object(
            CompatibleTenantWorkflowAPIClient, "get"
        )
        mock_tenant_workflow_config_get.return_value = addict.Dict(config_db_param)
        mocker.patch.object(CompatibleTenantWorkflowAPIClient, "update")

        # Mock static config API calls to avoid network requests
        mock_static_config = mocker.patch(
            "integration_audio_comms_tasks.transcription.transcription_copilot.transcription_copilot_flow.get_static_config"
        )
        from integration_audio_comms_tasks.transcription.transcription_copilot.input_schema import (
            TranscriptionCopilotStaticConfig,
        )
        mock_static_config.return_value = TranscriptionCopilotStaticConfig()

        # Mock auditor upload
        mock_auditor_upload = mocker.patch(
            "integration_wrapper.integration_aries_task.upload_audit"
        )
        mock_auditor_upload.return_value = None

        # Mock tenant configuration fetch
        tenant_config_mock = mocker.patch.object(FetchTenantConfiguration, "run")
        tenant_config_mock.return_value = {
            "featureFlags": [FeatureFlags.CALL_TRANSCRIPTION_COPILOT]
        }

        # Mock AI completions
        mock_completion = mocker.patch.object(
            SeOpenApiClient,
            "call_open_ai",
        )
        if completion_side_effect:
            mock_completion.side_effect = completion_side_effect
        else:
            mock_completion.return_value = mock_gpt_response

        if elasticsearch_mode:
            # Mock Elasticsearch operations
            mock_es(mocker, transcription_copilot_flow, pd.DataFrame())

            # Load test data
            call_data: list[dict] = []
            transcript_data: list[dict] = []
            with fsspec.open(INPUT_NDJSON_CALL, mode="r") as f:
                call_data.extend(json.loads(line) for line in f)
            with fsspec.open(INPUT_NDJSON_TRANSCRIPT, mode="r") as f:
                transcript_data.extend(json.loads(line) for line in f)

            # Mock search operations
            mock_search_yield = mocker.patch.object(
                transcription_copilot_flow, "fetch_transcribed_calls_which_need_copilot_enrichment"
            )
            mock_search_yield.return_value = iter([call_data] * 2)

            mock_search = mocker.patch.object(transcription_copilot_flow, "get_transcripts")
            mock_search.side_effect = [transcript_data] * 2

            # Mock spoken neural watches
            mock_neural_watches = mocker.patch.object(
                transcription_copilot_flow, "get_spoken_neural_watches"
            )
            mock_neural_watches.return_value = mock_spoken_neural_watches or []

            if es_output_files:
                mock_output_destination_fixture.side_effect = es_output_files
        else:
            # Mock file operations for file-based mode
            mock_file = mocker.patch.object(transcription_copilot_flow, "run_download_file")
            mock_file.side_effect = [INPUT_NDJSON_TRANSCRIPT, INPUT_NDJSON_CALL]
            mock_output_destination_fixture.side_effect = [
                TEMP_FILE_TRANSCRIPT.name,
                TEMP_FILE_CALL.name,
            ]

        # Run flow
        return transcription_copilot_task.transcription_copilot_run(  # type: ignore[attr-defined]
            aries_task_input=sample_aries_task_input
        )
