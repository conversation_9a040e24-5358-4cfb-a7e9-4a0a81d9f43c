import json
import pytest
from se_api_svc.messages.crud_endpoint import RecordCreated, RecordDeleted, RecordUpdated
from unittest.mock import MagicMock, patch


class TestRecordUpdated:
    def test_new_audit_record_basic(self):
        old_record = {"id": 1, "name": "original", "timestamp": 1}
        new_record = {"id": 1, "name": "updated", "timestamp": 2}
        key_before = "test_key"
        model = "TestModel"

        record_updated = RecordUpdated(
            old_record=old_record, new_record=new_record, key_before=key_before, model=model
        )

        with patch.object(RecordUpdated.__bases__[0], "new_audit_record") as mock_super:
            mock_audit_record = MagicMock()
            mock_audit_record.recordDetails = MagicMock()
            mock_audit_record.eventDetails = MagicMock()
            mock_audit_record.user = "test_user"
            mock_super.return_value = mock_audit_record

            result = record_updated.new_audit_record()

            assert result.recordDetails.originalRecordKey == key_before
            assert result.eventDetails.event == f"{model}RecordUpdated"
            assert result.eventDetails.description == f"test_user updated {model} record"

            expected_body = json.dumps(
                {"originalRecord": old_record, "updatedRecord": new_record}
            ).encode("utf-8")
            assert result.body == expected_body

    def test_new_audit_record_without_model(self):
        old_record = {"id": 1, "name": "original"}
        new_record = {"id": 1, "name": "updated"}
        key_before = "test_key"

        record_updated = RecordUpdated(
            old_record=old_record, new_record=new_record, key_before=key_before, model=None
        )

        with patch.object(RecordUpdated.__bases__[0], "new_audit_record") as mock_super:
            mock_audit_record = MagicMock()
            mock_audit_record.recordDetails = MagicMock()
            mock_audit_record.eventDetails = MagicMock()
            mock_audit_record.user = "test_user"
            mock_super.return_value = mock_audit_record

            result = record_updated.new_audit_record()

            assert result.recordDetails.originalRecordKey == key_before
            assert (
                not hasattr(result.eventDetails, "event")
                or result.eventDetails.event != "RecordUpdated"
            )
            assert not hasattr(result.eventDetails, "description") or "updated" not in str(
                result.eventDetails.description
            )

    def test_new_audit_record_json_serialization_error(self):
        old_record = {"id": 1, "name": "original"}
        new_record = {"id": 1, "name": "updated", "non_serializable": object()}
        key_before = "test_key"
        model = "TestModel"

        record_updated = RecordUpdated(
            old_record=old_record, new_record=new_record, key_before=key_before, model=model
        )

        with patch.object(RecordUpdated.__bases__[0], "new_audit_record") as mock_super:
            mock_audit_record = MagicMock()
            mock_audit_record.recordDetails = MagicMock()
            mock_audit_record.eventDetails = MagicMock()
            mock_audit_record.user = "test_user"
            mock_super.return_value = mock_audit_record

            with patch("se_api_svc.messages.crud_endpoint.logging") as mock_logging:
                result = record_updated.new_audit_record()

                mock_logging.error.assert_called_once()
                assert result.recordDetails.originalRecordKey == key_before

    def test_new_audit_record_without_user(self):
        old_record = {"id": 1, "name": "original"}
        new_record = {"id": 1, "name": "updated"}
        key_before = "test_key"
        model = "TestModel"

        record_updated = RecordUpdated(
            old_record=old_record, new_record=new_record, key_before=key_before, model=model
        )

        with patch.object(RecordUpdated.__bases__[0], "new_audit_record") as mock_super:
            mock_audit_record = MagicMock()
            mock_audit_record.recordDetails = MagicMock()
            mock_audit_record.eventDetails = MagicMock()
            mock_audit_record.user = None
            mock_super.return_value = mock_audit_record

            result = record_updated.new_audit_record()

            assert result.recordDetails.originalRecordKey == key_before
            assert result.eventDetails.event == f"{model}RecordUpdated"
            assert not hasattr(result.eventDetails, "description") or "updated" not in str(
                result.eventDetails.description
            )


class TestRecordCreated:
    def test_record_property(self):
        created_record = {"id": 1, "name": "new_record"}

        record_created = RecordCreated(created_record=created_record, model="TestModel")

        assert record_created.record == created_record

    def test_audit_description_with_record_and_model(self):
        created_record = {"id": 1, "name": "new_record"}
        model = "TestModel"

        record_created = RecordCreated(created_record=created_record, model=model)

        def tenancy():
            return MagicMock(userId="test_user_id")

        with patch("api_sdk.messages.base.DomainMessage.tenancy", new_callable=tenancy):
            expected_description = f"test_user_id created {model} record"
            assert record_created.audit_description == expected_description

    def test_audit_description_without_record(self):
        model = "TestModel"

        record_created = RecordCreated(created_record=None, model=model)

        def tenancy():
            return MagicMock(userId="test_user_id")

        with patch("api_sdk.messages.base.DomainMessage.tenancy", new_callable=tenancy):
            assert record_created.audit_description is None

    def test_audit_description_without_model(self):
        created_record = {"id": 1, "name": "new_record"}

        record_created = RecordCreated(created_record=created_record, model=None)

        def tenancy():
            return MagicMock(userId="test_user_id")

        with patch("api_sdk.messages.base.DomainMessage.tenancy", new_callable=tenancy):
            assert record_created.audit_description is None

    def test_audit_description_without_tenancy(self):
        created_record = {"id": 1, "name": "new_record"}
        model = "TestModel"

        record_created = RecordCreated(created_record=created_record, model=model)

        with pytest.raises(AttributeError):
            _ = record_created.audit_description


class TestRecordDeleted:
    def test_record_property(self):
        deleted_record = {"id": 1, "name": "deleted_record"}

        record_deleted = RecordDeleted(deleted_record=deleted_record, model="TestModel")

        assert record_deleted.record == deleted_record

    def test_audit_description_with_record_and_model(self):
        deleted_record = {"id": 1, "name": "deleted_record"}
        model = "TestModel"

        record_deleted = RecordDeleted(deleted_record=deleted_record, model=model)

        def tenancy():
            return MagicMock(userId="test_user_id")

        with patch("api_sdk.messages.base.DomainMessage.tenancy", new_callable=tenancy):
            expected_description = f"test_user_id deleted {model} record"
            assert record_deleted.audit_description == expected_description

    def test_audit_description_without_record(self):
        model = "TestModel"

        record_deleted = RecordDeleted(deleted_record=None, model=model)

        def tenancy():
            return MagicMock(userId="test_user_id")

        with patch("api_sdk.messages.base.DomainMessage.tenancy", new_callable=tenancy):
            assert record_deleted.audit_description is None

    def test_audit_description_without_model(self):
        deleted_record = {"id": 1, "name": "deleted_record"}

        record_deleted = RecordDeleted(deleted_record=deleted_record, model=None)

        def tenancy():
            return MagicMock(userId="test_user_id")

        with patch("api_sdk.messages.base.DomainMessage.tenancy", new_callable=tenancy):
            assert record_deleted.audit_description is None

    def test_audit_description_without_tenancy(self):
        deleted_record = {"id": 1, "name": "deleted_record"}
        model = "TestModel"

        record_deleted = RecordDeleted(deleted_record=deleted_record, model=model)

        with pytest.raises(AttributeError):
            _ = record_deleted.audit_description
