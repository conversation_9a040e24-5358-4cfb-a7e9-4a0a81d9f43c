import re
from api_sdk.schemas.base import APIModel
from api_sdk.utils.utils import StringEnum
from pydantic import Field, validator
from se_elastic_schema.components.surveillance.comms_surveillance_settings import (
    CommsSurveillanceSettings,
)
from se_elastic_schema.components.tenant.custom_resolution_categories import (
    CustomResolutionCategories,
)
from se_elastic_schema.static.tenant import ResolutionCategoryTypeVisibility
from typing import Any, Dict, List, Optional

DEFAULT_RESOLUTION_CATEGORY_VISIBILITY = CustomResolutionCategories(
    cSurvVisibility=ResolutionCategoryTypeVisibility.STEELEYE,
    tSurvVisibility=ResolutionCategoryTypeVisibility.STEELEYE,
)


class TenantConfigurationOut(APIModel):  # type: ignore
    emailDomains: Optional[List[str]] = Field(default_factory=list)
    loginLandingPage: Optional[str] = None
    subscribedMarketAbuseReports: Optional[List[str]] = Field(default_factory=list)
    subscribedModules: Optional[List[str]] = Field(default_factory=list)
    usageTrackingEnabled: Optional[bool] = None
    tenantId: Optional[str] = None
    featureFlags: Optional[List[str]] = Field(default_factory=list)
    userIdleTimeoutMs: Optional[int] = Field(default=3600000)
    ssoUrl: Optional[str] = None
    customerId: Optional[str] = None
    customResolutionCategories: Optional[CustomResolutionCategories] = None
    commsSurveillanceSettings: Optional[CommsSurveillanceSettings] = None
    uiSettings: Optional[Dict[str, Any]] = None
    idpManagedFeatures: Optional[bool] = Field(
        None,
        description="Flag indicating if the tenant's "
        "features are managed by IDP roles with keycloak",
    )
    translateCharactersLimit: Optional[int] = Field(
        None,
        description="Limit on the number of characters that can be translated on demand in month.",
    )

    @validator("customResolutionCategories")
    def set_default_visibility(cls, crc):
        return crc or DEFAULT_RESOLUTION_CATEGORY_VISIBILITY


class TenantFeatureFlags(StringEnum):
    COMMS_TRANSLATION = "comms-translation"
    LEXICA_PRE_PROCESSING = "lexicaPreprocessing"
    ALLOW_ZIP_UPLOAD = "allowZipUpload"
    DISABLE_BULK_RESOLVE_ALERT_EMAILS = "disable-bulk-resolve-alert-emails"


class UpdateCustomResolutionCategories(APIModel):
    cSurv: Optional[List[str]] = Field(None, description="List of custom csurv categories")
    tSurv: Optional[List[str]] = Field(None, description="List of custom tsurv categories")
    cSurvVisibility: Optional[ResolutionCategoryTypeVisibility] = Field(None)
    tSurvVisibility: Optional[ResolutionCategoryTypeVisibility] = Field(None)

    @validator("cSurv", "tSurv", pre=True, each_item=True)
    def validate_categories(cls, value):
        if not isinstance(value, str) or not value.strip():
            raise ValueError("Each item in the category list must be a non-empty string.")
        if not re.match(r"^[a-zA-Z0-9._:/\s-]+$", value):
            raise ValueError(
                "Each item in the category list can only contain alphanumeric characters, ., -, _,"
                "or :."
            )
        return value


class UpdateCommsSurveillanceSettings(APIModel):
    infoBarrierMaxRecipients: Optional[int] = Field(None)
    privacyEnforcementEnabled: Optional[bool] = Field(None)
