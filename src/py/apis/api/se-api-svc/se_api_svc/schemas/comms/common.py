# type: ignore
import pydantic
from api_sdk.schemas.base import APIModel, Field, RecordModel
from api_sdk.utils.utils import StringEnum
from enum import auto
from typing import List, Optional


class Body(pydantic.BaseModel):
    text: Optional[str]
    displayText: Optional[str]

    class Config:
        extra: pydantic.Extra.allow


class CommsParticipantType(StringEnum):
    COUNTERPARTY = auto()
    PEOPLE = auto()


class CommsTrendChart(StringEnum):
    COUNTERPARTIES = auto()
    COMMUNICATORS = auto()
    NUM_OF_PARTICIPANTS = auto()
    PARTICIPANT_TYPE = auto()
    SENDERS = auto()
    SOURCE = auto()
    TYPE = auto()


class GroupDetailsIn(APIModel):
    groupIds: List[str]


class GroupDetails(APIModel):
    counterparties: Optional[List[str]] = None
    date: Optional[str] = None
    detailLink: Optional[str] = None
    fromCounterparties: Optional[List[str]] = None
    fromParticipants: Optional[List[str]] = None
    fromIds: Optional[List[str]] = None
    numberOfMessages: Optional[int] = None
    participants: Optional[List[str]] = None
    roomId: Optional[str] = None
    threadId: Optional[str] = None
    toCounterparties: Optional[List[str]] = None
    toParticipants: Optional[List[str]] = None
    toIds: Optional[List[str]] = None

    extra = pydantic.Extra.allow


class DetailResponseSizing(StringEnum):
    FULL = auto()
    SLIM = auto()


class ParticipantsDirections(StringEnum):
    RECIPIENT = auto()
    SENDER = auto()


class ParticipantCategorizationTrends(StringEnum):
    CATEGORIZATION = auto()
    CATEGORIZATION_DETAILED = auto()


class ParticipantCategorizations(StringEnum):
    EXTERNAL_ONLY = "ExternalOnly"
    INTERNAL_AND_EXTERNAL = "InternalAndExternal"
    INTERNAL_ONLY = "InternalOnly"
    NO_PARTICIPANTS = "No Participants"
    NO_RECORDS = "No MyMarket Record"


class ParticipantCategorizationsDetailed(StringEnum):
    EXTERNAL_MULTIPLE = "ExternalMultiple"
    EXTERNAL_SINGLE = "ExternalSingle"
    INTERNAL_MULTIPLE = "InternalMultiple"
    INTERNAL_MULTIPLE_AND_EXTERNAL_MULTIPLE = "InternalMultipleAndExternalMultiple"
    INTERNAL_MULTIPLE_AND_EXTERNAL_SINGLE = "InternalMultipleAndExternalSingle"
    INTERNAL_SINGLE = "InternalSingle"
    INTERNAL_SINGLE_AND_EXTERNAL_SINGLE = "InternalSingleAndExternalSingle"
    INTERNAL_SINGLE_AND_EXTERNAL_MULTIPLE = "InternalSingleAndExternalMultiple"
    NO_PARTICIPANTS = "No Participants"
    NO_RECORDS = "No MyMarket Record"


class GetParticipantHeatmapIn(APIModel):
    day: int = Field(None, ge=1, le=7, title="Day of Week")
    hour: int = Field(None, ge=0, le=23, title="Hour of the Day")


class GetParticipantsIn(APIModel):
    categorizations: Optional[List[ParticipantCategorizations]] = Field([])
    categorizations_detailed: Optional[List[ParticipantCategorizationsDetailed]] = Field(
        [], alias="categorizationsDetailed"
    )
    directions: Optional[List[ParticipantsDirections]] = Field([])
    heatmaps: Optional[List[GetParticipantHeatmapIn]] = Field([])
    participant_ids: Optional[List[str]] = Field([], alias="participantIds")


class ParticipantTrends(StringEnum):
    NAME = auto()
    DOMAIN = auto()
    ID = auto()
    NATIONALITY = auto()
    HOME_ADDRESS_COUNTRY = auto()
    HOME_ADDRESS_CITY = auto()
    OFFICE_ADDRESS_COUNTRY = auto()
    OFFICE_ADDRESS_CITY = auto()
    CLIENT_MANDATE = auto()
    EMPLOYEE_ID = auto()
    TRADER_ID = auto()
    DECISION_MAKER = auto()
    INSTRUMENT = auto()
    ROLE = auto()
    SMCR_FUNCTION = auto()
    SMCR_FUNCTION_CATEGORY = auto()
    SMCR_REVIEW_DATE = auto()
    DEPARTMENT = auto()
    TYPE = auto()
    DESK_ID = auto()
    DESK = auto()
    JURISDICTION_BUSINESS_LINE = auto()
    JURISDICTION_COUNTRY = auto()
    JURISDICTION_CUSTOM1 = auto()
    JURISDICTION_CUSTOM2 = auto()
    JURISDICTION_CUSTOM3 = auto()
    JURISDICTION_LEGAL_ENTITY = auto()
    JURISDICTION_REGION = auto()
    IS_MONITORED = auto()
    IS_MONITORED_FOR = auto()
    MONITORING_RISK_LEVEL = auto()
    BRANCH_COUNTRY = auto()
    LOCAL_PARTS = auto()


class CommsTrends(StringEnum):
    DATA_SOURCE = "data-source"
    ANALYTICS_SOURCE = "analytics-source"
    HOUR = "hour"
    MODEL = "model"
    WEEK = "week"
    LABELS = "labels"
    CUSTOM_LABELS = "custom-labels"


class TimeOperator(StringEnum):
    OUTSIDE = auto()
    BETWEEN = auto()


class GetCommsTimeIn(APIModel):
    operator: Optional[TimeOperator] = None
    end: Optional[int] = None
    start: Optional[int] = None


class GetCommunicationIds(APIModel):
    ids: List[str] = None


class CommRecord(RecordModel):
    class Config:
        index_suffix = [
            "call",
            "email",
            "text",
            "message",
            "meeting",
        ]
        extra = pydantic.Extra.allow
