import stringcase  # pants: no-infer-dep
from api_sdk.messages.base import DomainEvent
from dataclasses import dataclass
from se_api_svc.messages.audit.events import (
    RecordModificationEvent,
)
from se_api_svc.schemas.common import RecordChange
from se_api_svc.schemas.track import ModuleTitle
from typing import List


@dataclass
class cSurvSettingsUpdateEvent(DomainEvent):
    audit_module = ModuleTitle.ADMIN

    @property
    def record(self):
        return {}


@dataclass
class cSurvWorkflowSettingsUpdated(cSurvSettingsUpdateEvent, RecordModificationEvent):
    tenant_config_id: str = None
    changes: List[RecordChange] = None
    event_description = (
        "Updated comms surveillance workflow setting '{setting}' "
        "from '{old_value}' to '{new_value}'"
    )

    def generate_audit_records(self, **kwargs):
        for change in self.changes or []:
            yield self.new_audit_record(
                description=self.event_description.format(
                    setting=stringcase.titlecase(change.field),
                    old_value=change.old_value,
                    new_value=change.new_value,
                ),
                recordId=self.tenant_config_id,
            )
