import logging
import stringcase  # pants: no-infer-dep
from api_sdk.messages.base import MessageBus
from fastapi import Request
from se_api_svc.messages.registry import registry
from se_api_svc.messages.tenancy.commands import CsurvSettingsUpdateCommand
from se_api_svc.messages.tenancy.events import cSurvWorkflowSettingsUpdated
from se_api_svc.repository.tenancy import TenantConfigurationRepository
from se_api_svc.schemas.common import RecordChange

log = logging.getLogger(__name__)


@registry.command_handler
async def csurv_settings_update(
    request: Request,
    command: CsurvSettingsUpdateCommand,
    repo: TenantConfigurationRepository,
    mb: MessageBus,
):
    """
    Handles updates to CSurv settings of tenant configuration.
    - Compares old and new settings.
    - Calls a specific handler method for each changed field (if implemented).
    - Publishes an event with the list of changes.
    """
    old_settings, new_settings = command.old_settings, command.new_settings
    changes = []
    for field, new_value in new_settings.items():
        old_value = old_settings.get(field)

        if old_value != new_value:
            changes.append(RecordChange(field=field, old_value=old_value, new_value=new_value))
            log.info(
                f"CSurv Settings Change detected: '{field}' from '{old_value}' → '{new_value}'"
            )
            # Find the handler method for this setting
            handler = getattr(
                repo,
                f"handle_{stringcase.snakecase(field)}_change",
                lambda *_, **__: None,  # No-op if handler not implemented
            )
            handler(old_value=old_value, new_value=new_value)

    await mb.publish(
        cSurvWorkflowSettingsUpdated(
            request=request,
            changes=changes,
            in_reply_to=command,
        )
    )
