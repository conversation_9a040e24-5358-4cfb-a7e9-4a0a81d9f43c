import json
import logging
from dataclasses import dataclass
from pydantic.main import BaseModel
from se_api_svc.messages.audit.events import (
    RecordCreationEvent,
    RecordModificationEvent,
)
from se_api_svc.schemas.track import (
    CategoryTitle,
    UserAudit,
)
from typing import Dict, Optional


class RecordModificationAuditDetails(BaseModel):
    originalRecord: Optional[dict] = None
    updatedRecord: Optional[dict] = None


@dataclass
class RecordUpdated(RecordModificationEvent):
    audit_category = CategoryTitle.RECORD_MODIFICATION
    old_record: Optional[dict] = None
    new_record: Optional[dict] = None
    model: str = None

    @property
    def record(self):
        return self.old_record

    def new_audit_record(self, **kwargs) -> UserAudit:
        record = super().new_audit_record(**kwargs)
        record.recordDetails.originalRecordKey = self.key_before
        update_details = RecordModificationAuditDetails(
            originalRecord=self.old_record,
            updatedRecord=self.new_record,
        )

        try:
            record.body = (json.dumps(update_details.dict())).encode("utf-8")
        except TypeError as e:
            logging.error(e)

        if self.model:
            record.eventDetails.event = f"{self.model}RecordUpdated"

        if record.user and self.model:
            record.eventDetails.description = f"{record.user} updated {self.model} record"

        return record


@dataclass
class RecordCreated(RecordCreationEvent):
    audit_category = CategoryTitle.RECORD_CREATION
    created_record: Dict = None
    model: str = None

    @property
    def record(self):
        return self.created_record

    @property
    def audit_description(self):
        desc = None
        user_id = self.tenancy.userId
        if self.record and self.model:
            desc = f"{user_id} created {self.model} record"

        return desc


@dataclass
class RecordDeleted(RecordCreationEvent):
    audit_category = CategoryTitle.RECORD_DELETION
    deleted_record: Dict = None
    model: str = None

    @property
    def record(self):
        return self.deleted_record

    @property
    def audit_description(self):
        desc = None
        user_id = self.tenancy.userId
        if self.record and self.model:
            desc = f"{user_id} deleted {self.model} record"

        return desc
