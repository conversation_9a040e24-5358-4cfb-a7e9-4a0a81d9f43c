# type: ignore
import logging
from addict import Dict
from api_sdk.auth import Tenancy
from api_sdk.config_base import ApiConfig
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.repository.elastic_abstract import AbstractSyncEsRepository
from api_sdk.repository.syncronous.request_bound import BoundRepository
from api_sdk.search_models.orders import OrdersWithExecutionsSearch
from api_tasks.repository.api_task_db_service import ApiTaskDbService
from case_bulk_workflow.base import CaseAttachSpec
from case_bulk_workflow.case_attach_records import CaseAttachRecordsByIdsTask
from case_bulk_workflow.case_delete_records import CaseDeleteRecordsByIdsTask
from case_bulk_workflow.schemas.case_bulk_in import BulkImportType, CaseBulkRecordIn
from case_bulk_workflow.schemas.common import model_map
from se_api_svc.core.constants import CASE_BULK_RECORDS_LIMIT
from se_api_svc.messages.cases import events
from se_api_svc.repository.mar.scenarios import ScenarioTagAndAlertsSearch
from se_api_svc.schemas.static import APIWorkflows
from se_api_svc.services.data_platform_config import DataPlatformConfig
from se_db_utils.database import Database
from se_elastic_schema.static.provenance import TaskStatus
from starlette.background import BackgroundTasks
from starlette.requests import Request
from typing import Optional

log = logging.getLogger(__name__)


class CaseBulkOperation:
    def __init__(
        self,
        data_platform_service: DataPlatformConfig,
        api_task_repo: ApiTaskDbService,
        tenancy: Tenancy,
        es_repo_sync: AbstractSyncEsRepository,
        api_config: ApiConfig,
        db: Database,
        owner: Optional[Request] = None,
    ):
        self.data_platform_service = data_platform_service
        self.api_task_repo = api_task_repo
        self.tenancy = tenancy
        self.record_handler = BoundRepository(
            tenancy=tenancy, es_repo=es_repo_sync, api_config=api_config
        )
        self.db = db
        self.owner = owner

    async def run_bulk_attach_records_to_case(
        self, bulk_request: CaseBulkRecordIn, api_task_id: str
    ):
        try:
            attach_task = CaseAttachRecordsByIdsTask(
                tenancy=self.tenancy,
                record_handler=self.record_handler,
                owner=self.owner,
                db=self.db,
            )
            case_bulk_output = attach_task.run(bulk_request=bulk_request)

            # processing case records
            for model, records in case_bulk_output.case_records.items():
                log.info(f"Saving {model} records")
                for record in records:
                    try:
                        record = model_map[model](**record)
                        await self.record_handler.save_new(
                            record, use_uuid=True, datetime_to_str=True
                        )
                    except Exception as e:
                        log.error(f"Error while saving the {model} record: {e}")
            # processing alert records
            for model, records in case_bulk_output.alert_records.items():
                log.info(f"Updating {model} records")
                for record in records:
                    try:
                        record = model_map[model](**record)
                        await self.record_handler.save_existing(record, datetime_to_str=True)
                    except Exception as e:
                        log.error(f"Error while updating the alert record: {e}")

            # After processing, create a task record with a status of 'PROCESSED'
            return self.api_task_repo.update(
                to_update={
                    "status": TaskStatus.PROCESSED,
                },
                id=api_task_id,
            )
        except Exception as e:
            return self.api_task_repo.update(
                to_update={"status": TaskStatus.ERRORED, "errorMessage": e}, id=api_task_id
            )

    async def run_bulk_delete_records_from_case(
        self, bulk_request: CaseBulkRecordIn, api_task_id: str
    ):
        try:
            delete_task = CaseDeleteRecordsByIdsTask(
                tenancy=self.tenancy,
                record_handler=self.record_handler,
                owner=self.owner,
                db=self.db,
            )
            case_bulk_output = delete_task.run(bulk_request=bulk_request)
            # Process alert records only, as deleting case_records will be handled in the task itself  # noqa: E501
            for model, records in case_bulk_output.alert_records.items():
                log.info(f"Updating {model} records")
                for record in records:
                    record = model_map[model](**record)
                    await self.record_handler.save_existing(record)

            # After processing, create a task record with a status of 'PROCESSED'
            return self.api_task_repo.update(
                to_update={
                    "status": TaskStatus.PROCESSED,
                },
                id=api_task_id,
            )
        except Exception as e:
            return self.api_task_repo.update(
                to_update={"status": TaskStatus.ERRORED, "errorMessage": e}, id=api_task_id
            )

    def get_bulk_request_count(
        self,
        identifier: str,
        **bulk_request,
    ):
        bulk_request = CaseBulkRecordIn(**bulk_request, case_id=identifier)
        count_of_records = 0
        # Add the count of alerts and scenarios to the total count
        task_spec = CaseAttachSpec(records_in=bulk_request.records)
        count_of_records += len(task_spec.alerts)
        filter_key, ids = task_spec.get_ids()
        # If the bulk request type is ORDERS, add the count of order state records
        if bulk_request.type == BulkImportType.ORDERS or (
            ids and filter_key == "&key" and ids[0].split(":")[0] == "Order"
        ):
            _, ids = task_spec.get_ids(keys_to_ids=True)
            count_of_records += self.record_handler.get_count(
                search_model=OrdersWithExecutionsSearch(order_id=ids)
            )
        else:
            count_of_records += len(ids)

        if task_spec.scenarios:
            count_of_records += self.record_handler.get_count(
                search_model=ScenarioTagAndAlertsSearch(scenario_ids=task_spec.scenarios)
            )

        return count_of_records

    async def process_bulk_request(
        self,
        identifier: str,
        task_name: str,
        background_tasks: BackgroundTasks,
        mb: FastApiMessageBus,
        request: Request,
        **bulk_request,
    ):
        bulk_request_ = CaseBulkRecordIn(**bulk_request, case_id=identifier)
        record_ids = (
            [
                item.get("id_") or item.get("key_")
                for item in bulk_request.get("records")
                if isinstance(item, dict) and (item.get("id_") or item.get("key_")) is not None
            ]
            if bulk_request
            else []
        )
        if record_ids:
            # audit that case records are attached
            await mb.publish(
                events.CaseRecordsCreated(
                    case=Dict(id_=identifier), case_record_ids=record_ids, request=request
                )
            )
        # Check if the current bulk request records count is below the predefined limit
        if not bulk_request_.use_filter and (
            self.get_bulk_request_count(identifier=identifier, **bulk_request)
            <= CASE_BULK_RECORDS_LIMIT
        ):
            api_task = self.api_task_repo.create(
                **{
                    "workflowId": "case_bulk_se_api_svc",
                    "status": TaskStatus.PROCESSING,
                    "meta": {"caseId": identifier},
                }
            )
            task = getattr(self, f"run_{task_name}")
            # Add the task to be run in the background
            background_tasks.add_task(
                task, bulk_request=bulk_request_, api_task_id=str(api_task["id"])
            )
            return api_task

        # If the bulk request exceeds the limit, sending a kafka event to trigger the case_bulk workflow  # noqa: E501
        workflow_trace_id = self.data_platform_service._send_kafka_event(
            workflow_name=APIWorkflows.CASE_BULK_WORKFLOW,
            io_params={
                "task_name": task_name,
                "bulk_request": bulk_request_.dict(),
            },
        )
        return {
            **self.api_task_repo.create(
                **{"workflowId": workflow_trace_id, "meta": {"caseId": identifier}}
            ),
            "isConductor": True,
        }
