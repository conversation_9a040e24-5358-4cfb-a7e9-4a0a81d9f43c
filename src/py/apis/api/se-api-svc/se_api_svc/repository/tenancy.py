import logging
from api_sdk.exceptions import MoreThanOneRecordError, TooManyActiveTenantConfigurations
from api_sdk.repository.asyncronous.request_bound import RepoHelpersMixin, TenantConfiguration
from api_sdk.schemas.common import PRIVACY_LABELS
from se_api_svc.schemas.cases.cases import CaseRecord
from se_api_svc.schemas.comms.common import CommRecord
from se_api_svc.schemas.surveillance.alerts import CommunicationAlert
from se_elastic_schema.static.reference import PrivacyProtectionStatus

log = logging.getLogger(__name__)


class TenantConfigurationRepository(RepoHelpersMixin):  # type: ignore[misc]
    async def get(self):
        try:
            return await self.repo.get_one(TenantConfiguration)
        except MoreThanOneRecordError:
            raise TooManyActiveTenantConfigurations()

    def handle_privacy_enforcement_enabled_change(self, old_value, new_value):
        """
        Handles changes to the privacy enforcement setting.
        When enabled: All records with the 'Private Communication' label are marked as PROTECTED.
        When disabled: Such records are marked as PREVIOUSLY_PROTECTED.
        """
        log.info(f"Updated the privacy enforcement setting to {new_value}")
        # Determine the new privacy protection status based on the setting value
        privacy_status = (
            PrivacyProtectionStatus.PROTECTED.value
            if new_value
            else PrivacyProtectionStatus.PREVIOUSLY_PROTECTED.value
        )

        body = {
            "script": {
                "source": "ctx._source.privacyProtectionStatus = params['status']",
                "params": {"status": privacy_status},
            },
            "query": {
                "bool": {
                    "must_not": {"exists": {"field": "&expiry"}},
                    "filter": {"terms": {"labels.labels": PRIVACY_LABELS}},
                }
            },
        }
        indices = []
        for record_model in (CaseRecord, CommRecord, CommunicationAlert):
            indices.extend(self.repo.index_for_record_model(record_model))

        log.info(f"Executing Query {body} @ {indices}")
        response = self.repo.es_client.update_by_query(
            index=indices,
            body=body,
            refresh=True,
        )
        log.info(f"Updated the privacy status of {response['updated']} records")
