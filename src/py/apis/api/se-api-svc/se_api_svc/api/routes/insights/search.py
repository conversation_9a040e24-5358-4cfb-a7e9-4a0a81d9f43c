import copy
import itertools
from api_sdk.database import Database
from api_sdk.di.request import ReqDep
from api_sdk.es_dsl.base import Or
from api_sdk.es_dsl.flang import SimpleQueryGenerator
from api_sdk.full_text_search import INSIGHTS_SEARCH_FIELDS, get_free_text_search_filters
from api_sdk.middleware.module_permission_checker import ModulePer<PERSON><PERSON>he<PERSON>
from api_sdk.models.request_params import PaginatedDatedListParams
from api_sdk.models.search import SearchResult
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from api_sdk.schemas.module_permission_checker import All
from api_sdk.schemas.static import Module
from api_sdk.static import FieldType
from elasticsearch8 import Elasticsearch
from fastapi import APIRouter, Body, Query
from fastapi.param_functions import Depends
from flang.es import build_query
from insights_query_utils.flattener import AggregationFlattener
from insights_query_utils.models.field import <PERSON><PERSON><PERSON><PERSON>ield
from insights_query_utils.query_builder import InsightsQueryBuilder
from schema_sdk.steeleye_model.gamma import GammaMetaFields
from se_api_svc.api.routes.insights.common import (
    INDEX_SUFFIXES_BY_MODULE,
    MODELS_BY_MODULE,
    RECORD_MODELS_BY_MODULE,
    InsightsModule,
)
from se_api_svc.core.constants import ES_SEARCH_ANALYZER
from se_api_svc.core.route_permissions import (
    case_manager_insights_building,
    comms_insights_building,
    csurv_insights_building,
    orders_insights_building,
    tsurv_insights_building,
)
from se_api_svc.repository.insights.insights import InsightsRepositoryAdapter
from se_api_svc.schemas.insights import InsightsDetailedSearch
from se_api_svc.services.refine_schema import RefineEncoder
from se_api_svc.utils.api import validate_permissions
from se_api_svc.utils.request import relative_path_for
from starlette.requests import Request
from typing import Dict, List, Optional, Union

router = APIRouter()

DEFAULT_INSIGHTS_MAX_AGG_SIZE = 5000
DEFAULT_INSIGHTS_MAX_DETAIL_SIZE = 5000

module_key_role_permission_map = {
    InsightsModule.CASE_MANAGER: case_manager_insights_building,
    InsightsModule.COMMS_SURVEILLANCE: csurv_insights_building,
    InsightsModule.COMMUNICATIONS: comms_insights_building,
    InsightsModule.ORDERS: orders_insights_building,
    InsightsModule.TRADE_SURVEILLANCE: tsurv_insights_building,
}


def _build_module_filters(
    module: InsightsModule,
    filters: Optional[Dict],
    search: Optional[str],
    insights_fields: List[InsightsField] = [],
) -> Dict:
    def append_filter(filters: Dict, filtr: dict):
        if isinstance(filters.get("bool", {}).get("must"), dict):
            # Remount the filters into a list
            filters["bool"]["must"] = [filters.get("bool").get("must"), filtr]
        elif isinstance(filters.get("bool", {}).get("must"), list):
            filters["bool"]["must"].append(filtr)
        elif filters:
            filters = {"bool": {"filter": [filters, filtr]}}
        else:
            filters = {"bool": {"filter": [filtr]}}
        return filters

    def get_validated_insight_search_fields():
        searchable_fields = []
        insight_search_module_mapping = INSIGHTS_SEARCH_FIELDS.get(module.name, {})
        nested_fields = (
            insight_search_module_mapping.get(FieldType.NESTED.value, {}).values()
            if isinstance(insight_search_module_mapping, dict)
            else {}
        )
        non_nested_fields = (
            insight_search_module_mapping.get(FieldType.NON_NESTED.value, [])
            if isinstance(insight_search_module_mapping, dict)
            else insight_search_module_mapping
        )

        insight_search_mapping = (
            list(itertools.chain.from_iterable(nested_fields)) + non_nested_fields
        )

        for field in insights_fields:
            field_name = getattr(field, "propertyId") + ES_SEARCH_ANALYZER
            if field_name in insight_search_mapping:
                searchable_fields.append(field_name)

        return searchable_fields

    if filters is None:
        filters = {}

    insight_search_fields = get_validated_insight_search_fields()
    # Don't search for an bunch of space characters
    if search and search.strip() and insight_search_fields:
        search_filters = Or(
            *get_free_text_search_filters(
                field_mapping=INSIGHTS_SEARCH_FIELDS.get(module.name, {}), search_value=search
            ),
        )

        filters = append_filter(
            filters,
            search_filters.to_dict(meta_fields=GammaMetaFields),
        )

    if module not in (InsightsModule.COMMS_SURVEILLANCE, InsightsModule.TRADE_SURVEILLANCE):
        return filters

    model = ["CommunicationAlert"]
    if module == InsightsModule.TRADE_SURVEILLANCE:
        model = ["OrderAlert", "MarketAbuseScenarioTag"]
    filters = append_filter(filters, {"terms": {"&model": model}})

    return filters


@router.post(
    f"/{Module.CASE_MANAGER.name}/insights/flattened-search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.CASE_MANAGER)))],
    name="insights:case_manager-surveillance:flattened-search",
    description="Perform flattened aggregation search for case manager insights data.",
)
@router.post(
    f"/{Module.COMMUNICATIONS.name}/insights/flattened-search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.COMMUNICATIONS)))],
    name="insights:comms-surveillance:flattened-search",
    description="Perform flattened aggregation search for communications insights data.",
)
@router.post(
    f"/{Module.COMMS_SURVEILLANCE.name}/insights/flattened-search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.COMMS_SURVEILLANCE)))],
    name="insights:csurv-surveillance:flattened-search",
    description="Perform flattened aggregation search "
    "for communications surveillance insights data.",
)
@router.post(
    f"/{Module.ORDERS.name}/insights/flattened-search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.ORDERS)))],
    name="insights:orders-surveillance:flattened-search",
    description="Perform flattened aggregation search for orders insights data.",
)
@router.post(
    f"/{Module.BEST_EXECUTION.name}/insights/flattened-search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.BEST_EXECUTION)))],
    name="insights:bestex:flattened-search",
    description="Perform flattened aggregation search for best execution insights data.",
)
@router.post(
    f"/{Module.TRADE_SURVEILLANCE.name}/insights/flattened-search",
    name="insights:trade-surveillance:flattened-search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.TRADE_SURVEILLANCE)))],
    description="Perform flattened aggregation search for trade surveillance insights data.",
)
async def flattened_search(
    request: Request,
    fields: Optional[List[Dict]] = Body(None),
    filters: Optional[Union[Dict, str]] = Body(None),
    filter_type: Optional[str] = Body(None),
    search: Optional[str] = Body(None),
    module: Optional[InsightsModule] = Query(None),
    value_as_string: Optional[bool] = Body(False),
    repo: RequestBoundRepository = ReqDep(RequestBoundRepository),
    db: Database = ReqDep(Database),
):
    module = module or InsightsModule.from_relative_path(
        relative_path_for(request), InsightsModule.ORDERS
    )
    insights_fields = [InsightsField.from_dict(**f) for f in fields or ()]

    await validate_permissions(request, module_key_role_permission_map.get(module), db)

    nested = [x for x in (RefineEncoder(MODELS_BY_MODULE[module])).get_nested()]
    if filters and filter_type == "FLANG":
        filters = build_query(filters, gen=SimpleQueryGenerator(nested_paths=nested)).to_dict()
        if module == InsightsModule.TRADE_SURVEILLANCE:
            scenario_ids = get_market_abuse_scenario_ids(
                filters=filters,
                es_client=repo.es_client,
                index=[
                    repo.index_for(["market_abuse_alert-alias", "market_abuse_scenario_tag-alias"])
                ],
            )
            if "bool" not in filters:
                # Create a bool query if not already present
                filters = {"bool": {"must": [filters]}}
            filters_copy = copy.deepcopy(filters)
            if scenario_ids:
                scenario_filters = update_filters_for_scenario_ids(
                    filters=filters_copy, scenario_ids=scenario_ids
                )
                # Use original filters for OrderAlerts and
                # scenario_filters for MarketAbuseScenarioTag
                filters = {
                    "bool": {
                        "should": [
                            {
                                "bool": {
                                    "must": [
                                        {"terms": {"&model": ["OrderAlert"]}},
                                        *filters["bool"]["must"],
                                    ]
                                }
                            },
                            {
                                "bool": {
                                    "must": [
                                        {"terms": {"&model": ["MarketAbuseScenarioTag"]}},
                                        *scenario_filters["bool"]["must"],
                                    ]
                                }
                            },
                        ]
                    }
                }

    insights_filters = _build_module_filters(
        module=module,
        filters=filters,
        search=search,
        insights_fields=insights_fields,
    )

    # Request should have timestamps.timestampStart range of at most one month.
    # ensure_date_range_filter(insights_filters)

    query_builder = InsightsQueryBuilder(
        fields=insights_fields,
        filters=insights_filters,
        model="Case" if module == InsightsModule.CASE_MANAGER else None,
        max_agg_size=DEFAULT_INSIGHTS_MAX_AGG_SIZE,
    )

    insights_client = InsightsRepositoryAdapter(
        repo=repo.es_repo,
        index=[repo.index_for(s) for s in INDEX_SUFFIXES_BY_MODULE[module]],
        policy=repo.policy_result,
    )

    return AggregationFlattener.flatten_aggs(
        await insights_client.search(query_builder.query), insights_fields, value_as_string
    )


# Get Paginated Insights records APIs.
@router.get(
    f"/{Module.COMMUNICATIONS.name}/insights/search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.COMMUNICATIONS)))],
    name="insights:comms-surveillance:search",
    description="Search and retrieve paginated communications insights records.",
)
@router.get(
    f"/{Module.CASE_MANAGER.name}/insights/search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.CASE_MANAGER)))],
    name="insights:case_manager-surveillance:search",
    description="Search and retrieve paginated case manager insights records.",
)
@router.get(
    f"/{Module.COMMS_SURVEILLANCE.name}/insights/search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.COMMS_SURVEILLANCE)))],
    name="insights:csurv-surveillance:search",
    description="Search and retrieve paginated communications surveillance insights records.",
)
@router.get(
    f"/{Module.ORDERS.name}/insights/search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.ORDERS)))],
    name="insights:orders-surveillance:search",
    description="Search and retrieve paginated orders insights records.",
)
@router.get(
    f"/{Module.TRADE_SURVEILLANCE.name}/insights/search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.TRADE_SURVEILLANCE)))],
    name="insights:trade-surveillance:search",
    description="Search and retrieve paginated trade surveillance insights records.",
)
@router.get(
    f"/{Module.BEST_EXECUTION.name}/insights/search",
    dependencies=[Depends(ModulePermissionChecker(All(Module.BEST_EXECUTION)))],
    name="insights:bestex:search",
    description="Search and retrieve paginated best execution insights records.",
)
async def get_insights_search(
    request: Request,
    params: PaginatedDatedListParams = Depends(),
    include_expiry: Optional[bool] = Query(default=False, alias="includeExpiry"),
    repo: RequestBoundRepository = ReqDep(RequestBoundRepository),
):
    scenario_ids = []

    module_key = InsightsModule.from_relative_path(
        relative_path_for(request), InsightsModule.ORDERS
    )
    if module_key == InsightsModule.TRADE_SURVEILLANCE and params.f not in [None, ""]:
        nested = [x for x in (RefineEncoder(MODELS_BY_MODULE[module_key])).get_nested()]
        filters = build_query(params.f, gen=SimpleQueryGenerator(nested_paths=nested)).to_dict()
        scenario_ids = get_market_abuse_scenario_ids(
            filters=filters,
            es_client=repo.es_client,
            index=[repo.index_for(["market_abuse_alert-alias", "market_abuse_scenario_tag-alias"])],
        )
    return SearchResult.from_raw_result(
        await repo.get_many(
            record_model=RECORD_MODELS_BY_MODULE[module_key],
            search_model_cls=InsightsDetailedSearch,
            module=module_key,
            scenario_ids=scenario_ids,
            include_expiry=include_expiry,
            **params.as_search_kwargs(as_model_qs=False),
        )
    )


def get_market_abuse_scenario_ids(filters: dict, es_client: Elasticsearch, index: List[str]):
    """
    :param filters: dict
    :param es_client: Elasticsearch
    :param index: List[str]
    :return:
    """
    if "bool" not in filters:
        # Create a bool query if not already present
        filters = {"bool": {"must": [filters]}}
    if "must_not" not in filters["bool"]:
        filters["bool"]["must_not"] = []
    filters["bool"]["must_not"].extend(
        [{"term": {"detail.watchType": "ON_DEMAND"}}, {"exists": {"field": "&expiry"}}]
    )
    aggs_query = {
        "query": filters,
        "aggs": {"unique_scenario_ids": {"terms": {"field": "scenarioId", "size": 10000}}},
        "size": 0,
    }
    response = es_client.search(
        body=aggs_query,
        index=index,
    )
    scenario_ids = [
        bucket["key"] for bucket in response["aggregations"]["unique_scenario_ids"]["buckets"]
    ]
    return scenario_ids


def update_filters_for_scenario_ids(filters: dict, scenario_ids: List[str]):
    """
    :param filters: dict - The existing filters dictionary to be updated
    :param scenario_ids: List[str] - A list of scenario IDs to be included in the terms filter
    :return: dict
    Update the given filters to include a terms filter for the provided scenario IDs.

    This function modifies the input filters dictionary to ensure that it includes a terms filter
    for the specified scenario IDs. If the filters dictionary does not already contain a boolean
    query, one is created. The function also retains any existing terms filters on 'scenarioId'
    and range filters.
    """
    filters["bool"]["must"].append({"terms": {"scenarioId": scenario_ids}})
    retained_filters = []
    if "must" in filters["bool"]:
        for condition in filters["bool"]["must"]:
            # Check if the condition is a terms filter on 'scenarioId'
            if "terms" in condition and "scenarioId" in condition["terms"]:
                retained_filters.append(condition)
            # Check if the condition is a range filter
            elif "range" in condition:
                retained_filters.append(condition)
        filters["bool"]["must"] = retained_filters
    return filters
