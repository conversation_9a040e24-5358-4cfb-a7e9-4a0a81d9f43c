import datetime
import logging
import re
from api_sdk.auth import Tenancy
from api_sdk.database import Database
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import BadInput, InvalidModulePermission
from api_sdk.middleware.module_permission_checker import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as MPC
from api_sdk.models.permissions import Permission
from api_sdk.models.request_params import PaginatedDatedListParams
from api_sdk.models.search import SearchResult
from api_sdk.responses import OkResponse
from api_sdk.schemas.module_permission_checker import All
from api_sdk.schemas.permission import ApprovalState
from api_sdk.schemas.static import Module
from api_sdk.utils.database import get_user_permission
from api_sdk.utils.meta import remove_invalid_ref_meta
from fastapi import APIRouter, Depends, HTTPException, status
from insights_query_utils.models.field import InsightsField
from se_api_svc.api.routes.insights.common import (
    MODULE_ENUM_NAMES,
    MODULE_NAMES,
    InsightsModule,
    InsightsModuleReportTypes,
)
from se_api_svc.core.route_permissions import (
    best_execution_insights_reporting,
    case_manager_insights_reporting,
    comms_insights_reporting,
    orders_insights_reporting,
    tsurv_insights_reporting,
)
from se_api_svc.repository.insights.insights import InsightsRepository
from se_api_svc.repository.surveillance.watches import WatchesRepository
from se_api_svc.schemas.insights import InsightsReport, InsightsReportUpdateSchedule
from se_api_svc.schemas.surveillance.watches import MetaInsightsReport, Watch
from se_api_svc.utils.api import validate_permissions
from starlette.requests import Request
from typing import Optional

router = APIRouter()

logger = logging.getLogger(__name__)


async def create_insights_watch(repo: WatchesRepository, record: InsightsReport):
    watch_record = remove_invalid_ref_meta(record.to_es_dict())
    watch: Watch = Watch(
        name=watch_record.get("name"),
        queryType=Watch.QueryType.INSIGHTS,
        query=MetaInsightsReport(**watch_record),
        actionEmails=watch_record.get("actionEmails"),
        status=Watch.Status.ACTIVE,
    )
    await repo.save_new(watch)


def is_email_format(id: str):
    return re.match(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$", id) is not None


# Create Report APIs
# @router.post("/insights/reports", name="insights:reports")
@router.post(
    f"/{Module.CASE_MANAGER.name}/insights/reports",
    name="post_case_manager_insights_reports",
    dependencies=[Depends(MPC(All(Module.CASE_MANAGER)))],
)
@router.post(
    f"/{Module.COMMS_SURVEILLANCE.name}/insights/reports",
    name="post_comms_surveillance_insights_reports",
    dependencies=[Depends(MPC(All(Module.COMMS_SURVEILLANCE)))],
)
@router.post(
    f"/{Module.COMMUNICATIONS.name}/insights/reports",
    name="post_communications_insights_reports",
    dependencies=[Depends(MPC(All(Module.COMMUNICATIONS)))],
)
@router.post(
    f"/{Module.ORDERS.name}/insights/reports",
    name="post_orders_insights_reports",
    dependencies=[Depends(MPC(All(Module.ORDERS)))],
)
@router.post(
    f"/{Module.BEST_EXECUTION.name}/insights/reports",
    name="post_best_execution_insights_reports",
    dependencies=[Depends(MPC(All(Module.BEST_EXECUTION)))],
)
@router.post(
    f"/{Module.TRADE_SURVEILLANCE.name}/insights/reports",
    name="post_trade_surveillance_insights_reports",
    dependencies=[Depends(MPC(All(Module.TRADE_SURVEILLANCE)))],
)
async def create_report(
    request: Request,
    record: InsightsReport,
    repo: InsightsRepository = ReqDep(InsightsRepository),
    surveillance_repo: WatchesRepository = ReqDep(WatchesRepository),
):
    # Sniff module part from url
    module_key = request.url.path.split("/")[-3]
    record.fields_ = InsightsField.only_db_keys(record.fields_)
    record.module = MODULE_ENUM_NAMES[module_key]
    record.createdBy = repo.tenancy.principal
    # for SSO user, principal will be UUID
    if not is_email_format(repo.tenancy.principal):
        record.createdBy = repo.tenancy.user_name
    record.created = datetime.datetime.utcnow()
    record.paused = False
    await repo.save_new(record)

    # Create SurveillanceWatch if scheduled report.
    if record.schedule:
        await create_insights_watch(repo=surveillance_repo, record=record)

    return OkResponse(created=True, id=record.id_)


module_key_role_module_permission_map = {
    InsightsModule.CASE_MANAGER: (All(Module.CASE_MANAGER), case_manager_insights_reporting),
    InsightsModule.COMMS_SURVEILLANCE: (
        All(Module.COMMS_SURVEILLANCE),
        comms_insights_reporting,
    ),
    InsightsModule.COMMUNICATIONS: (All(Module.COMMUNICATIONS), comms_insights_reporting),
    InsightsModule.ORDERS: (All(Module.ORDERS), orders_insights_reporting),
    InsightsModule.BEST_EXECUTION: (All(Module.BEST_EXECUTION), best_execution_insights_reporting),
    InsightsModule.TRADE_SURVEILLANCE: (
        All(Module.ORDERS, Permission.TRADE_SURVEILLANCE),
        tsurv_insights_reporting,
    ),
}


async def ensure_permission(request: Request, db, module: InsightsModule):
    # All enums in InsightsModule must have a
    # corresponding entry in module_key_role_module_permission_map
    role_module_permission = module_key_role_module_permission_map[module]
    permission = role_module_permission[1]
    await validate_permissions(request, permission, db)


def ensure_module_permission(request: Request, module: InsightsModule):
    try:
        # All enums in InsightsModule must have a
        # corresponding entry in module_key_role_module_permission_map
        module_permission, _ = module_key_role_module_permission_map[module]
        MPC._validate_module_permission(request, module_permission)
    except InvalidModulePermission as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"msg": "permission denied", "info": e.info},
        )


# Get Paginated Reports list APIs
# Individual report type has been defined here so that it doesn't overrides the
# GET report details API below.
# @router.get("/insights/reports", name="insights:reports")
# @router.get("/insights/reports/my-reports", name="insights:reports:my-reports")
# @router.get("/insights/reports/scheduled", name="insights:reports:scheduled")
# @router.get("/insights/reports/unscheduled", name="insights:reports:unscheduled")
# @router.get("/insights/reports/shared", name="insights:reports:shared")
# @router.get("/{module_key}/insights/reports", name="module:insights:reports")
@router.get(
    "/{module_key}/insights/reports/my-reports", name="module:insights:reports:my-reports"
)  # no permission
@router.get(
    "/{module_key}/insights/reports/scheduled", name="module:insights:reports:scheduled"
)  # If shared permission; include shared, else just my
@router.get(
    "/{module_key}/insights/reports/unscheduled",
    name="module:insights:reports:unscheduled",
    description="Get paginated list of unscheduled insights reports.",
)  # If shared permission; include shared, else just my
@router.get(
    "/{module_key}/insights/reports/shared",
    name="shared_reports",
    description="Get paginated list of shared insights reports across users.",
)  # sahi hai, no change
async def get_reports(
    request: Request,
    module_key: Optional[InsightsModule] = InsightsModule.ORDERS,
    params: PaginatedDatedListParams = Depends(),
    repo: InsightsRepository = ReqDep(InsightsRepository),
    tenancy: Tenancy = ReqDep(Tenancy),
    db: Database = ReqDep(Database),
):
    report_type = InsightsModuleReportTypes.get_report_type_from_request(request=request)

    ensure_module_permission(request, module_key)

    if report_type in [InsightsModuleReportTypes.UNSCHEDULED, InsightsModuleReportTypes.SCHEDULED]:
        # Schedules and un-schedules report types send both shared and my-reports.
        # If user don't have INSIGHT_REPORT_ACCESS permission, change scheduled
        # to myscheduled and unscheduled to myunscheduled

        module_and_permission = module_key_role_module_permission_map.get(module_key)

        if module_and_permission:
            permission = module_and_permission[1]
            try:
                user_permission = get_user_permission(
                    db, tenancy.tenant, repo.userId, permission[0], permission[1], permission[2]
                )
                if user_permission["allowed"] == ApprovalState.DENY:
                    report_type = (
                        InsightsModuleReportTypes.MY_SCHEDULED
                        if report_type == InsightsModuleReportTypes.SCHEDULED
                        else InsightsModuleReportTypes.MY_UNSCHEDULED
                    )
            except Exception:
                logger.error(f"Error while fetching user permission for {repo.userId}")

    elif report_type == InsightsModuleReportTypes.SHARED:
        await ensure_permission(request, db, module_key)

    return SearchResult.from_raw_result(
        await repo.get_reports(
            **params.as_search_kwargs(),
            report_type=report_type,
            created_by=repo.tenancy.principal
            if is_email_format(repo.tenancy.principal)
            else repo.tenancy.user_name,
            module=MODULE_NAMES[module_key],
        )
    )


# Get report APIs
# @router.get("/insights/reports/{report_id}", name="insights:reports:get-report")
@router.get(
    f"/{Module.CASE_MANAGER.name}/insights/reports/{{report_id}}",
    name="get_case_manager_insights_report",
    dependencies=[Depends(MPC(All(Module.CASE_MANAGER)))],
)
@router.get(
    f"/{Module.COMMS_SURVEILLANCE.name}/insights/reports/{{report_id}}",
    name="get_comms_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMS_SURVEILLANCE)))],
)
@router.get(
    f"/{Module.COMMUNICATIONS.name}/insights/reports/{{report_id}}",
    name="get_communications_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMUNICATIONS)))],
)
@router.get(
    f"/{Module.ORDERS.name}/insights/reports/{{report_id}}",
    name="get_orders_insights_report",
    dependencies=[Depends(MPC(All(Module.ORDERS)))],
)
@router.get(
    f"/{Module.BEST_EXECUTION.name}/insights/reports/{{report_id}}",
    name="get_best_execution_insights_report",
    dependencies=[Depends(MPC(All(Module.BEST_EXECUTION)))],
)
@router.get(
    f"/{Module.TRADE_SURVEILLANCE.name}/insights/reports/{{report_id}}",
    name="get_trade_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.TRADE_SURVEILLANCE)))],
)
async def get_report(
    report_id: str,
    repo: InsightsRepository = ReqDep(InsightsRepository),
):
    # ToDO: Check if report belongs to specific module.
    return await repo.get_one(id=report_id, record_model=InsightsReport)


# Delete Report by ID APIs
# @router.delete("/insights/reports/{report_id}", name="insights:reports:delete-report")
@router.delete(
    f"/{Module.CASE_MANAGER.name}/insights/reports/{{report_id}}",
    name="delete_case_manager_insights_report",
    dependencies=[Depends(MPC(All(Module.CASE_MANAGER)))],
)
@router.delete(
    f"/{Module.COMMS_SURVEILLANCE.name}/insights/reports/{{report_id}}",
    name="delete_comms_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMS_SURVEILLANCE)))],
)
@router.delete(
    f"/{Module.COMMUNICATIONS.name}/insights/reports/{{report_id}}",
    name="delete_communications_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMUNICATIONS)))],
)
@router.delete(
    f"/{Module.ORDERS.name}/insights/reports/{{report_id}}",
    name="delete_orders_insights_report",
    dependencies=[Depends(MPC(All(Module.ORDERS)))],
)
@router.delete(
    f"/{Module.BEST_EXECUTION.name}/insights/reports/{{report_id}}",
    name="delete_best_execution_insights_report",
    dependencies=[Depends(MPC(All(Module.BEST_EXECUTION)))],
)
@router.delete(
    f"/{Module.TRADE_SURVEILLANCE.name}/insights/reports/{{report_id}}",
    name="delete_trade_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.TRADE_SURVEILLANCE)))],
)
async def delete_report(
    report_id: str,
    repo: InsightsRepository = ReqDep(InsightsRepository),
    sureveillance_repo: WatchesRepository = ReqDep(WatchesRepository),
):
    # ToDO: Check if report belongs to specific module.

    record: InsightsReport = await repo.get_one(id=report_id, record_model=InsightsReport)
    await repo.delete_existing(record=record)

    watch: Watch = await sureveillance_repo.get_watch_by_query_id(query_id=report_id)
    if watch:
        await sureveillance_repo.delete_existing(record=watch)

    return OkResponse(deleted=True)


# Edit report APIs
# @router.put("/insights/reports/{report_id}", name="insights:reports:edit-report")
@router.put(
    f"/{Module.CASE_MANAGER.name}/insights/reports/{{report_id}}",
    name="edit_case_manager_insights_report",
    dependencies=[Depends(MPC(All(Module.CASE_MANAGER)))],
)
@router.put(
    f"/{Module.COMMS_SURVEILLANCE.name}/insights/reports/{{report_id}}",
    name="edit_comms_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMS_SURVEILLANCE)))],
)
@router.put(
    f"/{Module.COMMUNICATIONS.name}/insights/reports/{{report_id}}",
    name="edit_communications_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMUNICATIONS)))],
)
@router.put(
    f"/{Module.ORDERS.name}/insights/reports/{{report_id}}",
    name="edit_orders_insights_report",
    dependencies=[Depends(MPC(All(Module.ORDERS)))],
)
@router.put(
    f"/{Module.BEST_EXECUTION.name}/insights/reports/{{report_id}}",
    name="edit_best_execution_insights_report",
    dependencies=[Depends(MPC(All(Module.BEST_EXECUTION)))],
)
@router.put(
    f"/{Module.TRADE_SURVEILLANCE.name}/insights/reports/{{report_id}}",
    name="edit_trade_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.TRADE_SURVEILLANCE)))],
)
async def edit_report(
    report_id: str,
    record: InsightsReport,
    repo: InsightsRepository = ReqDep(InsightsRepository),
    sureveillance_repo: WatchesRepository = ReqDep(WatchesRepository),
):
    # ToDo: Check if report belongs to specific module.

    # Update InsightsReport record
    record.fields_ = InsightsField.only_db_keys(record.fields_)
    del record.version_
    await repo.save_existing(record)

    # If schedule is True get watch details. If Sureveillance watch is present
    # update the watch else create one
    # Else delete the scheduled watch if present.
    if record.schedule:
        watch: Watch = await sureveillance_repo.get_watch_by_query_id(query_id=report_id)
        if watch:
            watch_query = record.to_es_dict()
            watch_query.pop("&version")
            watch_query.pop("&hash")
            watch.query = watch_query
            await sureveillance_repo.save_existing(watch)
        else:
            await create_insights_watch(repo=sureveillance_repo, record=record)

    return OkResponse(updated=True)


# Pause report APIs
# @router.put("/insights/reports/{report_id}/pause", name="insights:reports:pause-report")
# @router.put("/{module_key}/insights/reports/{report_id}/pause",
# name="module:insights:reports:pause-report")
@router.put(
    f"/{Module.CASE_MANAGER.name}/insights/reports/{{report_id}}/pause",
    name="pause_case_manager_insights_report",
    dependencies=[Depends(MPC(All(Module.CASE_MANAGER)))],
)
@router.put(
    f"/{Module.COMMS_SURVEILLANCE.name}/insights/reports/{{report_id}}/pause",
    name="pause_comms_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMS_SURVEILLANCE)))],
)
@router.put(
    f"/{Module.COMMUNICATIONS.name}/insights/reports/{{report_id}}/pause",
    name="pause_communications_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMUNICATIONS)))],
)
@router.put(
    f"/{Module.ORDERS.name}/insights/reports/{{report_id}}/pause",
    name="pause_orders_insights_report",
    dependencies=[Depends(MPC(All(Module.ORDERS)))],
)
@router.put(
    f"/{Module.BEST_EXECUTION.name}/insights/reports/{{report_id}}/pause",
    name="pause_best_execution_insights_report",
    dependencies=[Depends(MPC(All(Module.BEST_EXECUTION)))],
)
@router.put(
    f"/{Module.TRADE_SURVEILLANCE.name}/insights/reports/{{report_id}}/pause",
    name="pause_trade_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.TRADE_SURVEILLANCE)))],
)
async def pause_report(
    report_id: str,
    repo: InsightsRepository = ReqDep(InsightsRepository),
    sureveillance_repo: WatchesRepository = ReqDep(WatchesRepository),
):
    # Update InsightsReport record by setting paused=True
    record: InsightsReport = await repo.get_one(id=report_id, record_model=InsightsReport)
    record.paused = True
    record.schedule = False
    del record.version_
    await repo.save_existing(record)

    watch: Watch = await sureveillance_repo.get_watch_by_query_id(query_id=report_id)
    if watch:
        watch.status = Watch.Status.PAUSED
        watch_query = record.to_es_dict()
        watch_query.pop("&version")
        watch_query.pop("&hash")
        watch.query = watch_query
        await sureveillance_repo.save_existing(watch)

    return OkResponse(updated=True)


# Resume report APIs
# @router.put("/insights/reports/{report_id}/resume", name="insights:reports:resume-report")
# @router.put("/{module_key}/insights/reports/{report_id}/resume",
# name="module:insights:reports:resume-report")
@router.put(
    f"/{Module.CASE_MANAGER.name}/insights/reports/{{report_id}}/resume",
    name="resume_case_manager_insights_report",
    dependencies=[Depends(MPC(All(Module.CASE_MANAGER)))],
)
@router.put(
    f"/{Module.COMMS_SURVEILLANCE.name}/insights/reports/{{report_id}}/resume",
    name="resume_comms_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMS_SURVEILLANCE)))],
)
@router.put(
    f"/{Module.COMMUNICATIONS.name}/insights/reports/{{report_id}}/resume",
    name="resume_communications_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMUNICATIONS)))],
)
@router.put(
    f"/{Module.ORDERS.name}/insights/reports/{{report_id}}/resume",
    name="resume_orders_insights_report",
    dependencies=[Depends(MPC(All(Module.ORDERS)))],
)
@router.put(
    f"/{Module.BEST_EXECUTION.name}/insights/reports/{{report_id}}/resume",
    name="resume_best_execution_insights_report",
    dependencies=[Depends(MPC(All(Module.BEST_EXECUTION)))],
)
@router.put(
    f"/{Module.TRADE_SURVEILLANCE.name}/insights/reports/{{report_id}}/resume",
    name="resume_trade_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.TRADE_SURVEILLANCE)))],
)
async def resume_report(
    report_id: str,
    repo: InsightsRepository = ReqDep(InsightsRepository),
    sureveillance_repo: WatchesRepository = ReqDep(WatchesRepository),
):
    # Update InsightsReport record by setting paused=False
    record: InsightsReport = await repo.get_one(id=report_id, record_model=InsightsReport)
    record.paused = False
    del record.version_
    await repo.save_existing(record)

    watch: Watch = await sureveillance_repo.get_watch_by_query_id(query_id=report_id)
    if watch:
        watch.status = Watch.Status.ACTIVE
        watch_query = record.to_es_dict()
        watch_query.pop("&version")
        watch_query.pop("&hash")
        watch.query = watch_query
        await sureveillance_repo.save_existing(watch)

    return OkResponse(updated=True)


# Update Schedule report APIs
# @router.put("/insights/reports/{report_id}/update-schedule",
# name="insights:reports:update-schedule-report")
@router.put(
    f"/{Module.CASE_MANAGER.name}/insights/reports/{{report_id}}/update-schedule",
    name="update_schedule_case_manager_insights_report",
    dependencies=[Depends(MPC(All(Module.CASE_MANAGER)))],
)
@router.put(
    f"/{Module.COMMS_SURVEILLANCE.name}/insights/reports/{{report_id}}/update-schedule",
    name="update_schedule_comms_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMS_SURVEILLANCE)))],
)
@router.put(
    f"/{Module.COMMUNICATIONS.name}/insights/reports/{{report_id}}/update-schedule",
    name="update_schedule_communications_insights_report",
    dependencies=[Depends(MPC(All(Module.COMMUNICATIONS)))],
)
@router.put(
    f"/{Module.ORDERS.name}/insights/reports/{{report_id}}/update-schedule",
    name="update_schedule_orders_insights_report",
    dependencies=[Depends(MPC(All(Module.ORDERS)))],
)
@router.put(
    f"/{Module.BEST_EXECUTION.name}/insights/reports/{{report_id}}/update-schedule",
    name="update_schedule_best_execution_insights_report",
    dependencies=[Depends(MPC(All(Module.BEST_EXECUTION)))],
)
@router.put(
    f"/{Module.TRADE_SURVEILLANCE.name}/insights/reports/{{report_id}}/update-schedule",
    name="update_schedule_trade_surveillance_insights_report",
    dependencies=[Depends(MPC(All(Module.TRADE_SURVEILLANCE)))],
)
async def update_schedule_report(
    report_id: str,
    body: InsightsReportUpdateSchedule,
    repo: InsightsRepository = ReqDep(InsightsRepository),
    sureveillance_repo: WatchesRepository = ReqDep(WatchesRepository),
):
    """This endpoint expects the following payload:

    { actionEmails: [] }
    Step 1: Retrieve the report by ID
    Step 2: Update the report's props:
        actionEmails: payload.actionEmails
        paused: false
        schedule: true
    Step 3: Update the report
    Step 4: Find the report's existing schedule
        If there is one:
            Step 4a: set Schedule to Active and update schedule.query -> report
            Step 4b: Update the schedule
        If there is NOT:
            Step 4c: Create a new schedule for the report
    """
    if not body.action_emails:
        raise BadInput(msg="Action emails expected", loc=["body", "actionEmails"])

    # Step 1:
    record: InsightsReport = await repo.get_one(id=report_id, record_model=InsightsReport)

    # Step 2:
    record.actionEmails = body.action_emails
    record.paused = False
    record.schedule = True

    # Step 3:
    del record.version_
    await repo.save_existing(record)

    # Step 4:
    watch: Watch = await sureveillance_repo.get_watch_by_query_id(query_id=report_id)
    if watch:
        watch.status = Watch.Status.ACTIVE
        watch_query = remove_invalid_ref_meta(record.to_es_dict())
        watch.query = watch_query
        await sureveillance_repo.save_existing(watch)
    else:
        await create_insights_watch(repo=sureveillance_repo, record=record)

    return OkResponse(updated=True)
