import logging
from api_sdk.auth import Tenancy
from api_sdk.config_base import ApiConfig
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import NotFound
from api_sdk.models.session import Session
from api_sdk.repository.asyncronous.request_bound import has_feature_flag
from api_sdk.repository.syncronous.request_bound import TenantConfiguration
from api_sdk.responses import OkResponse
from api_sdk.schemas.kc import JWTToken, RefreshToken
from api_sdk.security.auth.utilities import (
    __camel_to_snake,
    __put_attr,
    fetch_models_for,
    unrestricted,
)
from api_sdk.security.exceptions import AuthenticationException, TenantException
from api_sdk.services.kc.kc import KeyCloakService
from fastapi import APIRouter, BackgroundTasks, Body, Depends, HTTPException, status
from se_api_svc.middleware.deps.kc import kc_denied_route
from se_api_svc.repository.account.users import UserRepository
from se_api_svc.repository.tenancy import TenantConfigurationRepository
from se_api_svc.schemas.account import AccountUser
from se_api_svc.schemas.auth import AuthenticateIn, EmailIn, OtpNeededOut, SSODetail
from se_api_svc.services.kc_sync.kc_sync import KcSyncService
from se_elastic_schema.models.tenant.security.user_session import UserSession
from se_elastic_schema.static.security import SubjectAttribute
from starlette.authentication import AuthenticationError
from starlette.requests import Request
from typing import Optional

router = APIRouter()

log = logging.getLogger(__name__)


@router.get(
    "/sso-url",
    name="auth:get-sso-url",
    tags=["auth/sso"],
    response_model=SSODetail,
    description="Get single sign-on configuration details and URL for the tenant.",
)
async def get_sso_url(
    tenancy: Tenancy = ReqDep(Tenancy),
    service: KeyCloakService = ReqDep(KeyCloakService),
    repo: TenantConfigurationRepository = ReqDep(TenantConfigurationRepository, skip_policy=True),
    config: ApiConfig = ReqDep(ApiConfig),
):
    tenant_config: TenantConfiguration = await repo.get()
    sso_detail = SSODetail(ssoEnabled=False, kcEnabled=has_feature_flag(tenant_config, "kc"))

    if tenancy.kc_enabled and has_feature_flag(tenant_config, "ssoEnabled"):
        sso_detail.ssoEnabled = True
        sso_detail.ssoUrl = service.sso_url(
            realm=tenancy.tenant,
            tenant_realm=tenancy.realm,
        )

    elif tenant_config.ssoUrl:
        sso_detail.ssoEnabled = True
        sso_detail.ssoUrl = tenant_config.ssoUrl

    elif has_feature_flag(tenant_config, "ssoEnabled"):
        sso_detail.ssoEnabled = True
        sso_detail.ssoUrl = f"https://auth.{repo.realm}/oauth2/authorize"

    if has_feature_flag(tenant_config, "sso-only"):
        sso_detail.ssoOnly = True

    if config.TRUSTED_AUTH_PLUGIN and repo.tenant in config.ING_TRUST_AUTH_ALLOWED_TENANTS:
        sso_detail.trustedAuth = True

    return sso_detail


@router.post(
    "/authenticate",
    name="auth:authenticate",
    tags=["auth/auth"],
    description="Authenticate user credentials and establish a new user session.",
)
async def authenticate(
    request: Request,
    tenancy: Tenancy = ReqDep(Tenancy),
    kc_sync: KcSyncService = ReqDep(KcSyncService),
    users_repo: UserRepository = ReqDep(UserRepository),
    auth_in: Optional[AuthenticateIn] = Body(None),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    kc: KeyCloakService = ReqDep(KeyCloakService),
) -> Session | UserSession:
    try:
        res = request.app.auth_manager.authenticate_and_save(
            auth_header=request.headers.get("Authorization", "__none__ none"),
            realm=tenancy.realm,
            tenant=tenancy.tenant,
            kc_enabled=tenancy.kc_enabled,
            proxy_user=request.headers.get("X-Proxy-User"),
            session_token=request.headers.get("X-Session-Token"),
            remote_addr=request.headers.get("X-Forwarded-For"),
            email=request.headers.get("X-Email")
            or request.headers.get("x-email"),  # for ing trusted auth plugin
            otp=auth_in.otp if auth_in else None,
            # Below will be used while account creation for CODE plugin.
            idp_managed_features=tenancy.tenant_config.idpManagedFeatures,
        )

        if tenancy.tenant_config.idpManagedFeatures:
            if tenancy.kc_enabled:
                try:
                    await kc_sync.sync_data(
                        res,
                        auth_header=request.headers.get("Authorization", "__none__ none"),
                        background_tasks=background_tasks,
                    )
                    # ToDo: @sumeet: Move this to api-sdk
                    account_user = await users_repo.get_one(
                        id=res.user_id, record_model=AccountUser
                    )
                    attributes = unrestricted(account_user.dict())
                    attrs = {__camel_to_snake(k): v for k, v in attributes.items()}
                    __put_attr(
                        attrs, SubjectAttribute.MODELS.value, fetch_models_for(account_user.dict())
                    )
                    res.attributes = Session.Attributes.parse_obj(attrs)
                except ValueError as e:
                    kc.logout(res.token.refresh_token, tenancy.tenant)
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST, detail={"errors": e.args[0]}
                    )
            else:
                log.error("Keycloak is not enabled for this tenant, but idpManagedFeatures is set")

        return res
    except TenantException:
        log.error(f"Unknown tenancy '{tenancy.realm}'")
        raise NotFound(model=Tenancy, id=tenancy.realm)
    except AuthenticationException as e:
        raise HTTPException(status_code=e.status, detail=e.error)


@router.post("/refresh", tags=["auth/auth"])
def refresh_access_token(
    tenancy: Tenancy = ReqDep(Tenancy),
    refresh_token: RefreshToken = Body(...),
    ks_service: KeyCloakService = ReqDep(KeyCloakService),
) -> JWTToken:
    """
    Refresh access token, this will just return the JWT token details and not user attributes.
    """
    return JWTToken(**ks_service.refresh_access_token(refresh_token.refresh_token, tenancy.tenant))


@router.post(
    "/mfa",
    name="auth:mfa",
    tags=["auth/auth"],
    dependencies=[Depends(kc_denied_route)],
    description="Perform multi-factor authentication using OTP verification.",
)
def mfa(request: Request, tenancy: Tenancy = ReqDep(Tenancy)):
    return request.app.auth_manager.perform_mfa(
        auth_header=request.headers.get("Authorization", "__none__ none"),
        otp=request.query_params.get("otp", "__none__"),
        realm=tenancy.realm,
    )


@router.post(
    "/logout",
    name="auth:logout",
    tags=["auth/auth"],
    description="Logout user and invalidate the current session.",
)
def logout(
    request: Request,
    tenancy: Tenancy = ReqDep(Tenancy),
    refresh_token: Optional[RefreshToken] = Body(None),
):
    if tenancy.kc_enabled and (refresh_token is None or refresh_token.refresh_token is None):
        raise HTTPException(status_code=400, detail="Refresh token is required when KC is enabled")

    request.app.auth_manager.logout(
        auth_header=request.headers.get("Authorization", "__none__ none"),
        realm=tenancy.realm,
        tenant=tenancy.tenant,
        email=request.headers.get("X-Email")
        or request.headers.get("x-email"),  # for ing trusted auth plugin
        kc_enabled=tenancy.kc_enabled,
        refresh_token=refresh_token.refresh_token if refresh_token else None,
    )
    return OkResponse()


@router.post(
    "/reauthenticate",
    name="auth:reauthenticate",
    tags=["auth/auth"],
    dependencies=[Depends(kc_denied_route)],
)
def reauthenticate(request: Request, tenancy: Tenancy = ReqDep(Tenancy)) -> UserSession:
    """Post ReAuthenticate."""
    return request.app.auth_manager.reauthenticate(
        auth_header=request.headers.get("Authorization", "__none__ none"),
        realm=tenancy.realm,
        email=request.headers.get("X-Email")
        or request.headers.get("x-email"),  # for ing trusted auth plugin
    )


@router.post(
    "/validate-sso",
    name="auth:validate-sso",
    tags=["auth/sso"],
    dependencies=[Depends(kc_denied_route)],
)
def validate_sso(
    request: Request, tenancy: Tenancy = ReqDep(Tenancy), config: ApiConfig = ReqDep(ApiConfig)
) -> UserSession:
    """
    Post Validate SSO - it's just a sugar coating for authenticate
    """
    auth_header = request.headers.get("Authorization", "__none__ none")

    # When trusted auth is enabled we don't need to check
    # All client authentication check will inside plugin
    # for instance if ing_tenant is not in allowed tenant
    # we would pass this but the plugin before session creation will raise error
    trusted_auth_enabled = config.TRUSTED_AUTH_PLUGIN

    if not trusted_auth_enabled and not auth_header.startswith("Bearer"):
        raise AuthenticationError("unknown authorization for validate-sso")

    return request.app.auth_manager.authenticate_and_save(
        auth_header=auth_header,
        realm=tenancy.realm,
        proxy_user=request.headers.get("X-Proxy-User"),
        session_token=request.headers.get("X-Session-Token"),
        remote_addr=request.headers.get("X-Forwarded-For"),
        email=request.headers.get("X-Email")
        or request.headers.get("x-email"),  # for ing trusted auth plugin
    )


@router.post("/keycloak/otp-required", tags=["auth/auth"])
def otp_required(
    tenancy: Tenancy = ReqDep(Tenancy),
    service: KeyCloakService = ReqDep(KeyCloakService),
    email: EmailIn = Body(...),
) -> OtpNeededOut:
    """
    Check if OTP is needed for the user
    """

    if not tenancy.kc_enabled:
        raise HTTPException(status_code=400, detail="This endpoint is for keycloak tenants only")

    return OtpNeededOut(otpRequired=service.otp_required(email=email.email, realm=tenancy.tenant))
