# type: ignore
import json
import logging
import pandas as pd
import sqlalchemy.exc
import uuid
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import AlreadyExists, NotFound
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.request_params import (
    CustomPageParams,
    PaginatedDatedListParams,
    PaginatedListParams,
)
from api_sdk.models.search import Pagination, SearchResult
from api_sdk.responses import OkResponse
from api_sdk.schemas.module_permission_checker import Any
from api_sdk.schemas.permission import ApprovalState
from api_sdk.schemas.static import Module
from api_sdk.services.xlsx_exporter import Fields
from api_sdk.utils.utils import nested_get
from cachetools import TTLCache
from fastapi import APIRouter, BackgroundTasks, Body, Depends, Query, Request, status
from fastapi.exceptions import HTTPException
from fastapi.responses import J<PERSON>NResponse
from json import JSONDecodeError
from pydantic import UUID4
from se_api_svc.api.deps.tenant_config import prevent_for_idp_managed_features
from se_api_svc.api.routes.admin.constants import (
    STEELEYE_DEFAULT_ROLE,
    permision_name_map,
    sub_module_name_map,
)
from se_api_svc.core.route_permissions import (
    VIEW_ROLE_PERMISSION,
    assign_roles_permission,
    edit_roles_permission,
    view_roles_permission,
)
from se_api_svc.messages.admin.events import (
    BulkAssignRoleAuditEvent,
    PermissionUpdateEvent,
    RoleCreationEvent,
    RoleDeletedEvent,
    RolesDownloadEvent,
    RolesUpdatedEvent,
    RolesViewEvent,
)
from se_api_svc.repository.account.users import UserRepository
from se_api_svc.repository.admin.exceptions import AssignedRole, DefaultRole
from se_api_svc.repository.admin.permissions import PermissionsRepo
from se_api_svc.repository.admin.roles import (
    ASSIGNED,
    UNASSIGNED,
    RolePermissionsRepo,
    RolesRepo,
    UserRoleRepo,
    UserRoleStatus,
)
from se_api_svc.repository.audit import AuditRepository
from se_api_svc.repository.download.create_file import CreateFile
from se_api_svc.repository.user_comments import UserCommentsRepository
from se_api_svc.schemas.admin.permission import PermissionIn
from se_api_svc.schemas.admin.roles import (
    RoleIn,
    RoleOut,
    RolesUpdateIn,
    UserRoleResponse,
)
from se_api_svc.schemas.role import BulkRoleAssignIn
from se_api_svc.schemas.track import CategoryTitle
from se_api_svc.utils.api import validate_module_permissions, validate_permissions
from se_api_svc.utils.audit import get_changes_list
from se_api_svc.utils.df_excel import dict_to_df, to_excel
from se_db_utils.database import Database
from tenant_db.models.roles_permissions.user_role import UserRole
from typing import Dict, List, Optional

log = logging.getLogger(__name__)

router = APIRouter()

logger = logging.getLogger(__name__)
module_roles_cache = TTLCache(maxsize=128, ttl=60 * 10)  # 10 minutes

audit_field = [
    Fields.String(dpath="timestamp", label="Timestamp"),
    Fields.String(dpath="user", label="User Name"),
    Fields.String(dpath="user", label="User ID"),
    Fields.String(dpath="email", label="Email"),
    Fields.String(dpath="eventDetails.category", label="Category"),
    Fields.String(dpath="eventDetails.module", label="Module"),
    Fields.String(dpath="eventDetails.event", label="Event"),
]

comment_fields = [
    Fields.MillisecondsDateTime(dpath="linkId", label="Role ID"),
    Fields.String(dpath="comment", label="Comment"),
]


async def populate_users_data_from_es(repo, users, params):
    params = params.as_search_kwargs()
    pagination = params.pop("pagination")
    user_data_dict = {user["userId"]: user for user in users}
    results = await repo.get_users(
        user_ids=list(user_data_dict.keys()),
        pagination=Pagination(take=pagination.take, sorts=pagination.sorts),
        **params,
    )
    results = results.dict()
    for user in results["hits"]["hits"]:
        user.update(user_data_dict[user["id_"]])

    return results["hits"]["hits"]


async def validate_view_roles_permission(request: Request, db: Database = ReqDep(Database)):
    module = request.path_params.get("module")
    permission_required = view_roles_permission.get(module)
    await validate_permissions(request, permission_required, db)


@router.get("/modules/{module}/summary", dependencies=[Depends(validate_view_roles_permission)])
async def get_role_modules_summary(
    module: Module,
    repo: RolesRepo = ReqDep(RolesRepo),
):
    """For a given module, get all roles, with permissions."""
    return repo.get_module_role_with_perms(module)


@router.get("/audits", name="account:role:get-all-audits")
async def get_audits(
    params: PaginatedDatedListParams = Depends(),
    repo: AuditRepository = ReqDep(AuditRepository),
):
    result = await repo.get_audits(
        category=CategoryTitle.ROLE,
        **params.as_search_kwargs(),
    )
    return SearchResult.from_raw_result(result, skipped_hits=params.page_params.skip)


async def validate_edit_role_permission(request: Request, db: Database = ReqDep(Database)):
    body_in = await request.body()
    try:
        role_in = RoleIn.parse_obj(json.loads(body_in))
    except JSONDecodeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "msg": f"invalid json body. {str(e)}",
            },
        )
    module = role_in.module
    permission_required = edit_roles_permission.get(module)
    await validate_permissions(request, permission_required, db)


@router.post("", response_model=RoleOut, dependencies=[Depends(validate_edit_role_permission)])
async def create_role(
    role_in: RoleIn = Body(...),
    repo: RolesRepo = ReqDep(RolesRepo),
    mb: FastApiMessageBus = Depends(),
):
    try:
        role_id = repo.create_role(role=role_in)
        role = await repo.get_role(role_id=role_id)
    except AlreadyExists:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail={
                "msg": "role already exists for module",
                "role": role_in.name,
                "module": role_in.module,
            },
        )
    await mb.publish(RoleCreationEvent(role=role))
    return role


@router.get("")
async def get_roles(
    request: Request,
    module: Optional[str] = Query(None),
    params: PaginatedDatedListParams = Depends(),
    repo: RolesRepo = ReqDep(RolesRepo),
    mb: FastApiMessageBus = Depends(),
):
    user_module_permisisons = nested_get(request, "state.tenancy.session.attributes.PERMISSIONS")
    if not user_module_permisisons:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, detail="Module permisisons not found in session"
        )

    params.page_params = CustomPageParams(take=10_00)

    allowed_modules = set(repo.get_modules_for_permission(VIEW_ROLE_PERMISSION))
    if Module.ADMIN not in user_module_permisisons:
        if module and module not in allowed_modules:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"User does not have permission to the module: {module}",
            )
        allowed_modules = {module} if module else allowed_modules
        roles = await repo.get_roles(module=allowed_modules, **params.as_search_kwargs())
    else:
        allowed_modules = {module} if module else None
        roles = await repo.get_roles(module=allowed_modules, **params.as_search_kwargs())

    await mb.publish(RolesViewEvent())
    return roles


@router.post("/download")
async def download_one_or_many_roles(
    role_id: uuid.UUID = Query(None, alias="roleId"),
    params: PaginatedDatedListParams = Depends(),
    repo: RolesRepo = ReqDep(RolesRepo),
    user_repo: UserRepository = ReqDep(UserRepository),
    permission_repo: PermissionsRepo = ReqDep(PermissionsRepo),
    audit_repo: AuditRepository = ReqDep(AuditRepository),
    comment_repo: UserCommentsRepository = ReqDep(UserCommentsRepository),
    create_file: CreateFile = ReqDep(CreateFile),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    mb: FastApiMessageBus = Depends(),
):
    create_file.draft(directory="roles", filename=f"roles_{CreateFile.suffix()}.xlsx")

    if role_id:  # Error if single role not found
        role = await repo.get_role(role_id=role_id)
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role '{role_id}' does not exist",
            )

    def replace_permission_name(df):
        df = df.replace(ApprovalState.ALLOW.name, "Yes")
        df = df.replace(ApprovalState.DENY.name, "No")
        df = df.replace(ApprovalState.WITH_APPROVAL.name, "Yes, With Approval")
        return df

    def get_module_matrix(summary: List[Dict[str, Any]]):
        if len(summary) == 0:
            return

        # Create df with column names
        roles = [role["name"] for role in summary]
        columns = ["Category", "Permission", *roles]
        df = pd.DataFrame(columns=columns)
        category_permissions = [
            (permission["sub_module"], permission["permission"])
            for permission in summary[0]["permissions"]
        ]

        df["Category"] = [
            sub_module_name_map.get(category_perm[0], category_perm[0])
            for category_perm in category_permissions
        ]
        df["Permission"] = [
            permision_name_map.get(category_perm[1], category_perm[1])
            for category_perm in category_permissions
        ]

        for role in summary:
            df[role["name"]] = [perm["allowed"] for perm in role["permissions"]]

        df = replace_permission_name(df)

        return df

    async def process(draft_file: CreateFile):
        try:
            # Single or all roles df
            roles_df = repo.download_roles(role_id=role_id, search=params.search)
            dfs = [roles_df]
            sheet_names = ["Roles"]
            create_file.filename = f"role_{CreateFile.suffix()}.xlsx"

            # If single role, download, permissions, users, comments, audits
            if role_id:
                # Adding roles will have to be the first thing.
                dfs = [roles_df[(roles_df["Id"] == role_id)]]
                create_file.filename = f"role_{role.name}_{CreateFile.suffix()}.xlsx"
                sheet_names = ["Role details"]

                # Permissions
                permissions = permission_repo.role_permissions(role=role)
                permissions_df = pd.DataFrame([dict(p) for p in permissions])
                permissions_df = permissions_df.rename(
                    columns={
                        "permission": "Permission",
                        "module": "Module",
                        "subModule": "Permission Category",
                        "allowed": "Allowed",
                    }
                )
                dfs.append(replace_permission_name(permissions_df))
                sheet_names.append("Permissions")

                # Role users
                user_field = [
                    Fields.String(dpath="userId", label="User ID"),
                    Fields.String(dpath="name", label="Name"),
                    Fields.String(dpath="email", label="Email"),
                ]
                role_users = repo.get_role_users(role)
                results = await user_repo.get_users(
                    user_ids=[user.userId for user in role_users],
                    pagination=Pagination.construct(take=10000),
                )
                role_users_dicts = [user.dict() for user in results.as_list()]
                dfs.append(dict_to_df(role_users_dicts, user_field))
                sheet_names.append("Assigned Users")

                # Not Role Users
                not_role_users = repo.get_not_role_users(role)
                results = await user_repo.get_users(
                    user_ids=[user.userId for user in not_role_users],
                    pagination=Pagination.construct(take=10000),
                )
                not_role_users_dicts = [user.dict() for user in results.as_list()]
                dfs.append(dict_to_df(not_role_users_dicts, user_field))
                sheet_names.append("Un-Assigned Users")
            else:
                # Matrices
                modules = roles_df["Module"].unique()
                for module in modules:
                    summary = repo.get_module_role_with_perms(module, search=params.search)
                    dfs.append(get_module_matrix(summary))
                    sheet_names.append(module)

            role_ids = list(roles_df["Id"])
            # Audits
            audits = (
                await audit_repo.get_audits(
                    record_id=str(role_id) if role_id else [str(roleId) for roleId in role_ids],
                    **params.as_search_kwargs(as_model_qs=False),
                )
            ).as_list()
            audits_dicts_df = dict_to_df(
                [audit.dict() for audit in audits],
                audit_field,
            )
            dfs.append(audits_dicts_df)
            sheet_names.append("Audits")

            # Comments
            comments = (
                await comment_repo.get_user_comments(
                    link_id=str(role_id) if role_id else [str(roleId) for roleId in role_ids]
                )
            ).as_list()
            comments_dicts_df = dict_to_df(
                [comment.dict() for comment in comments],
                comment_fields,
            )
            dfs.append(comments_dicts_df)
            sheet_names.append("Comments")

            roles_excel = to_excel(dfs=dfs, sheet_names=sheet_names)
            draft_file.save_obj(roles_excel)

        except Exception as e:
            logger.error(f"Error downloading roles: {e}")
            draft_file.fail("Internal server error")

    background_tasks.add_task(process, create_file)

    await mb.publish(RolesDownloadEvent(role_id=str(role_id)))

    return JSONResponse(
        status_code=status.HTTP_201_CREATED, content={"download_id": str(create_file.download_id)}
    )


@router.get("/{role_id}", response_model=RoleOut)
async def get_role(
    request: Request,
    role_id: UUID4,
    repo: RolesRepo = ReqDep(RolesRepo),
    db: Database = ReqDep(Database),
    mb: FastApiMessageBus = Depends(),
):
    role = await repo.get_role(role_id=role_id)
    if not role:
        raise NotFound(message="Role does not exist!")

    # Populate applied/not-applied  user counts if exists
    role = RoleOut.from_orm(role)

    # Check permission
    # ToDo: Move this to proper place, like dependency
    permission_required = view_roles_permission.get(role.module)
    await validate_permissions(request, permission_required, db)

    # populate assigned, unassigned
    role.assigned, role.unassigned = (
        repo._get_role_user_count(role_id=role_id)
        .get(str(role_id), {ASSIGNED: 0, UNASSIGNED: 0})
        .values()
    )

    await mb.publish(RolesViewEvent(role=role))

    return role


@router.put("/{role_id}")
async def update_role(
    request: Request,
    role_id: UUID4,
    role_in: RolesUpdateIn = Body(...),
    repo: RolesRepo = ReqDep(RolesRepo),
    db: Database = ReqDep(Database),
    mb: FastApiMessageBus = Depends(),
):
    role = await repo.get_role(role_id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Role '{role_id}' does not exist",
        )

    # TODO: create test
    if role.createdBy == STEELEYE_DEFAULT_ROLE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot update steelEye admin role",
        )

    # Check permission
    # ToDo: Move this to proper place, like dependency
    permission_required = edit_roles_permission.get(role.module)
    await validate_permissions(request, permission_required, db)

    repo.update_role(role=role, role_in=role_in)

    await mb.publish(
        RolesUpdatedEvent(
            role=role,
            changes=get_changes_list(
                old_data=RolesUpdateIn.from_orm(role).dict(), new_data=role_in.dict()
            ),
        )
    )

    return OkResponse()


@router.delete("/{role_id}", description="Delete a non-admin role")
async def delete_role(
    request: Request,
    role_id: uuid.UUID,
    repo: RolesRepo = ReqDep(RolesRepo),
    db: Database = ReqDep(Database),
    mb: FastApiMessageBus = Depends(),
):
    try:
        role = await repo.get_role(role_id=role_id)

        # Check permission
        # ToDo: Move this to proper place, like dependency
        permission_required = edit_roles_permission.get(role.module)
        await validate_permissions(request, permission_required, db)

        # TODO: create test
        if role.createdBy == STEELEYE_DEFAULT_ROLE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete steelEye admin role",
            )

        repo.delete_role(role_id)
    except (DefaultRole, AssignedRole) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Cannot delete role because - {str(e)}"
        )
    except NotFound:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="role not found")
    except Exception as e:
        log.error(e)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Error deleting role"
        )

    await mb.publish(RoleDeletedEvent(role))

    return JSONResponse(
        status_code=status.HTTP_200_OK, content={"message": "Role deleted successfully"}
    )


@router.post("/{role_id}/permissions", name="account:role:permissions:add-or-update")
async def upsert_permissions(
    request: Request,
    role_id: UUID4,
    permissions: List[PermissionIn] = Body(...),
    reset: Optional[bool] = Query(
        False, description="Delete all the old permissions, and set given permissions"
    ),
    repo: RolesRepo = ReqDep(RolesRepo),
    db: Database = ReqDep(Database),
    mb: FastApiMessageBus = Depends(),
):
    role = await repo.get_role(role_id=role_id)

    # TODO: create test
    if role.createdBy == STEELEYE_DEFAULT_ROLE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot update steelEye admin role's permission",
        )

    current_permissions = await repo.get_role_permissions(role_id=str(role_id))

    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Role '{role_id}' does not exist",
        )

    # Check permission
    # ToDo: Move this to proper place, like dependency
    permission_required = edit_roles_permission.get(role.module)
    await validate_permissions(request, permission_required, db)

    repo.add_permissions(role, permissions, reset=reset)

    # Update Role audit fields
    repo.update_role(
        role=role,
        role_in=RolesUpdateIn.construct(
            name=role.name, description=role.description, is_default=role.isDefault
        ),
    )

    await mb.publish(
        PermissionUpdateEvent(
            role=role,
            old_permissions={item["name"]: item for item in current_permissions},
            new_permissions={item.name: item.dict() for item in permissions},
        )
    )

    return OkResponse()


@router.get("/{role_id}/permissions", name="account:role:get-permissions")
async def get_role_permissions(
    request: Request,
    role_id: UUID4,
    repo: PermissionsRepo = ReqDep(PermissionsRepo),
    roles_repo: RolesRepo = ReqDep(RolesRepo),
    db: Database = ReqDep(Database),
):
    role = await roles_repo.get_role(role_id=role_id)
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Role '{role_id}' does not exist",
        )

    # Check permission
    # ToDo: Move this to proper place, like dependency
    permission_required = view_roles_permission.get(role.module)
    await validate_permissions(request, permission_required, db)

    return repo.role_permissions(role)


@router.get("/{role_id}/users", name="account:role:get-users")
async def get_users(
    role_id: UUID4,
    params: PaginatedListParams = Depends(),
    role_status: UserRoleStatus = Query(UserRoleStatus.APPLIED, alias="roleStatus"),
    repo: UserRoleRepo = ReqDep(UserRoleRepo),
    user_repo: UserRepository = ReqDep(UserRepository),
):
    params_dict = params.as_search_kwargs(as_model_qs=False)

    # Handle user search and retrieve user data
    users_data_dict = None

    search_term = params_dict.pop("search", None)
    sort_column = params.page_params.sort.split(":")[0] if params.page_params.sort else None

    if search_term or sort_column in [col.name for col in UserRole.__table__.columns]:
        users = await user_repo.get_user_ids(
            pagination=Pagination.construct(take=10_000),
            search=search_term,
        )
        users_data_dict = {user["id_"]: user for user in users.dict()["hits"]["hits"]}
        params_dict["user_ids"] = list(users_data_dict.keys())

    headers, users = await repo.get_users_with_module_role(
        role_id=str(role_id), status=role_status, **params_dict
    )

    # Merge users data with roles if available
    if users_data_dict:
        results = [
            user.update(users_data_dict[user["userId"]]) or user
            for user in users
            if user["userId"] in users_data_dict
        ]
    else:
        results = await populate_users_data_from_es(user_repo, users, params)

    return UserRoleResponse(**{"headers": headers, "hits": results}).dict()


@router.get("/{module}/permissions", name="account:role:get-permissions-by-module")
async def get_permissions_by_module(
    request: Request,
    module: str,
    repo: RolePermissionsRepo = ReqDep(RolePermissionsRepo),
    sub_module: Optional[str] = Query(None, alias="subModule"),
    db: Database = ReqDep(Database),
):
    """Get list of permissions for a module.

    Parameters
    ----------
    module (modules): module name
    sub_module (subModules): sub module name

    Returns
    -------
    list:
        list of permissions
    """

    # Check permission
    # ToDo: Move this to proper place, like dependency
    permission_required = edit_roles_permission.get(module)
    await validate_permissions(request, permission_required, db)

    return await repo.get_permissions_by_module(module=module, sub_module=sub_module)


@router.get("/{module}/users", name="role:get-module-users", response_model=UserRoleResponse)
async def get_module_users(
    request: Request,
    module: str,
    params: PaginatedListParams = Depends(),
    repo: UserRoleRepo = ReqDep(UserRoleRepo),
    user_repo: UserRepository = ReqDep(UserRepository),
    db: Database = ReqDep(Database),
):
    # Check permission
    # ToDo: Move this to proper place, like dependency
    permission_required = edit_roles_permission.get(module)
    await validate_permissions(request, permission_required, db)

    headers, users = await repo.get_users_with_module_role(
        module=module, **params.as_search_kwargs()
    )

    results = await populate_users_data_from_es(user_repo, users, params)

    return {"headers": headers, "hits": results}


@router.get("/{role_id}/audits", name="account:role:get-single-role-audits")
async def get_single_role_audits(
    role_id: UUID4,
    params: PaginatedDatedListParams = Depends(),
    repo: AuditRepository = ReqDep(AuditRepository),
):
    result = await repo.get_audits(
        record_id=str(role_id),
        **params.as_search_kwargs(),
    )
    return SearchResult.from_raw_result(result, skipped_hits=params.page_params.skip)


async def validate_user_assign_permission(request: Request, db: Database = ReqDep(Database)):
    module = request.query_params.get("module")
    if not module:
        # If module not provided then user should have admin permission
        validate_module_permissions(request, Any(Module.ADMIN))
    else:
        permission_required = assign_roles_permission.get(module)
        await validate_permissions(request, permission_required, db)


@router.post(
    "/users",
    name="roles:users:assign",
    dependencies=[
        Depends(validate_user_assign_permission),
        Depends(prevent_for_idp_managed_features),
    ],
    description="Bulk assign or reassign roles to multiple users with validation.",
)
async def bulk_assign_roles(
    module: Optional[Module] = Query(None),
    repo: RolesRepo = ReqDep(RolesRepo),
    user_repo: UserRepository = ReqDep(UserRepository),
    bulk_assign_in: BulkRoleAssignIn = Body(...),  # Ex - {role_id : [user_id]}
    mb: FastApiMessageBus = Depends(),
):
    # Validating user IDs
    user_ids = BulkRoleAssignIn.get_user_ids(bulk_assign_in.assign_roles)
    users = await user_repo.get_user_ids(
        pagination=Pagination.construct(take=10_000), user_ids=user_ids
    )
    es_users_set = {user.id_ for user in users.hits.hits}
    if es_users_set != user_ids:
        users_not_found = user_ids - es_users_set
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"users {list(users_not_found)} not found",
        )

    # Validating user Role IDs
    try:
        role_ids = bulk_assign_in.get_role_ids()
        roles = repo.get_roles_by_id(role_ids=list(role_ids))
        roles_dict = {str(role.id): role for role in roles}
        bulk_assign_in.roles = roles
    except sqlalchemy.exc.DataError:
        log.debug(f"Invalid role ids: {role_ids}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"msg": "Invalid role IDs", "roleIDs": list(role_ids)},
        )
    if len(role_ids) != len(roles_dict.keys()):
        log.debug(f"Invalid role ids: {role_ids}")
        invalid_roles = set(role_ids) - set(roles_dict.keys())
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"roles {list(invalid_roles)} not found",
        )

    # Check if all roles belong to same module, if non-admin user is doing the assignment.
    if module:
        for role in roles:
            if role.module != module:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"mdg": f"role {role.id} does not belong to module {module.value}"},
                )

    if bulk_assign_in.deleting:
        if bulk_assign_in.replace is None:
            # Validate that default role exists if replace role not provided.
            deleting_role = await repo.get_role(bulk_assign_in.deleting)
            default_role = repo.default_role(deleting_role.module)
            if default_role is None:
                log.debug(f"default role not found for module {deleting_role.module}")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"default role not found for module {deleting_role.module}",
                )
            bulk_assign_in.replace = str(default_role.id)
        else:
            # Validate that deleting role and replace role are of same module.
            delete_replace = await repo.get_roles(
                role_id=[bulk_assign_in.deleting, bulk_assign_in.replace]
            )
            if delete_replace["hits"][0]["module"] != delete_replace["hits"][1]["module"]:
                log.debug("deleting and replace role should be of same module")
                log.debug(f"deleting role: {delete_replace['hits'][0]['module']}")
                log.debug(f"replace role: {delete_replace['hits'][1]['module']}")

                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="deleting and replace role should be of same module",
                )

    if bulk_assign_in.assign_roles:
        repo.assign_roles(user_role_map=bulk_assign_in.assign_roles, roles=roles_dict)

    if bulk_assign_in.deleting and bulk_assign_in.replace:
        repo.replace_role(bulk_assign_in.deleting, bulk_assign_in.replace)

    await mb.publish(BulkAssignRoleAuditEvent(payload=bulk_assign_in))

    return OkResponse()


@router.get("/users/{user_id}/permissions")
async def get_user_role_permissions(user_id: str, repo: UserRoleRepo = ReqDep(UserRoleRepo)):
    return await repo.get_user_permissions(user_id)
