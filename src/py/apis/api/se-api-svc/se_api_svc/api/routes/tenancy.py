import logging
from api_sdk.auth import require_permissions
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import BackendError, ModelIntegrityError
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.request_params import NonPaginatedDatedListParams, PaginatedListParams
from api_sdk.models.search import SearchResult
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from elasticsearch.exceptions import RequestError  # pants: no-infer-dep
from fastapi import APIRouter, Body, Depends
from se_api_svc.messages.tenancy.commands import CsurvSettingsUpdateCommand
from se_api_svc.permissions import TENANCY_ADMIN
from se_api_svc.repository.tenancy import TenantConfigurationRepository
from se_api_svc.schemas.tenancy import (
    TenantConfigurationOut,
    UpdateCommsSurveillanceSettings,
    UpdateCustomResolutionCategories,
)
from se_api_svc.schemas.upload import TenantDataSource
from se_elastic_schema.components.surveillance.comms_surveillance_settings import (
    CommsSurveillanceSettings,
)
from typing import Dict, Optional

# from app.utils.api import caching_service

log = logging.getLogger(__name__)
router = APIRouter()


# added to follow pattern of all existing APIs
@router.get(
    "/tenancy/configurations",
    name="tenancy:tenant-configurations:get",
    response_model=TenantConfigurationOut,
)
@router.get(
    "/tenant-configuration",
    name="tenancy:tenant-configuration",
    response_model=TenantConfigurationOut,
)
# @caching_service.cache("5m")
async def get_tenant_configuration(
    repo: TenantConfigurationRepository = ReqDep(TenantConfigurationRepository, skip_policy=True),
    params: NonPaginatedDatedListParams = Depends(),
):
    """Get the current tenant's configuration."""
    try:
        return await repo.get()
    except (RuntimeError, ModelIntegrityError) as error:
        log.error("Missing tenant configuration for %s: %s", repo.tenant, error)
        raise BackendError()
    except RequestError as error:
        log.warning("Failed to retrieve tenant data from ES due to a request error: %s.", error)
        raise BackendError()


@router.get(
    "/tenancy/data-sources", name="tenancy:data-sources:get-all", response_model=SearchResult
)
async def get_all_tenant_data_sources(
    params: PaginatedListParams = Depends(),
    repo: RequestBoundRepository = ReqDep(RequestBoundRepository),
):
    return SearchResult.from_raw_result(
        await repo.get_many(record_model=TenantDataSource, **params.as_search_kwargs())
    )


@router.put(
    "/tenancy/custom-resolution-categories",
    name="tenancy:custom-resolution-categories",
    response_model=TenantConfigurationOut,
)
@require_permissions(TENANCY_ADMIN)
async def update_custom_resolution_categories(
    repo: TenantConfigurationRepository = ReqDep(TenantConfigurationRepository),
    custom_resolution_categories: UpdateCustomResolutionCategories = Body(...),
):
    tenant_config = await repo.get()
    tenant_config.customResolutionCategories = custom_resolution_categories
    await repo.save_existing(tenant_config)
    return tenant_config


@router.put("/tenancy/csurv-settings", response_model=TenantConfigurationOut)
@require_permissions(TENANCY_ADMIN)
async def update_csurv_settings(
    repo: TenantConfigurationRepository = ReqDep(TenantConfigurationRepository),
    csurv_settings: UpdateCommsSurveillanceSettings = Body(...),
    mb: FastApiMessageBus = Depends(),
):
    """Updates the `commsSurveillanceSettings` configuration for a tenant."""
    try:
        tenant_config = await repo.get()
        old_settings = (
            tenant_config.commsSurveillanceSettings or CommsSurveillanceSettings()
        ).dict(exclude_none=True)
        new_settings = csurv_settings.dict(exclude_none=True)

        log.info(f"Updating tenant configuration with changes: {new_settings}")
        tenant_config.commsSurveillanceSettings = CommsSurveillanceSettings(
            **{**old_settings, **new_settings}
        )
        await repo.save_existing(tenant_config)

        await mb.request(
            CsurvSettingsUpdateCommand(old_settings=old_settings, new_settings=new_settings)
        )
        return tenant_config

    except BackendError as e:
        log.error(f"Failed to update comms surveillance settings, error: {e}")
        raise BackendError()


@router.put(
    "/tenancy/update-ui-settings",
    name="tenancy:update-ui-settings",
    response_model=TenantConfigurationOut,
)
@require_permissions(TENANCY_ADMIN)
async def update_ui_settings(
    repo: TenantConfigurationRepository = ReqDep(TenantConfigurationRepository),
    new_ui_settings: Optional[Dict] = Body(None),
):
    tenant_config = await repo.get()
    tenant_config.uiSettings = new_ui_settings
    await repo.save_existing(tenant_config)
    return await repo.get()
