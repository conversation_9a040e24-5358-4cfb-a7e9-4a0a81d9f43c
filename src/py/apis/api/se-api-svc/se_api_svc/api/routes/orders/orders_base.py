import http
import logging
import re
from api_sdk.di.request import ReqDep
from api_sdk.exception_handlers import master_data_exception_handler
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.messages.orders.events import OrdersSearched, OrderViewed, RefinitivNewsSearched
from api_sdk.middleware.module_permission_checker import <PERSON><PERSON><PERSON>Per<PERSON><PERSON>he<PERSON>
from api_sdk.models.elasticsearch import RawResult
from api_sdk.models.request_params import (
    CustomPageParams,
    DatedListParams,
    NewsParams,
    NonPaginatedDatedListParams,
    NonPaginatedListParams,
    PaginatedDatedListParams,
    PaginatedListParams,
)
from api_sdk.models.search import SearchResult
from api_sdk.schemas.module_permission_checker import All
from api_sdk.schemas.orders.order import Order
from api_sdk.schemas.static import Module
from api_sdk.services.master_data import MasterDataClient
from api_sdk.services.master_data_news import MasterDataNewsService
from api_sdk.utils.intervals import TimeInterval
from api_sdk.utils.utils import b64decode_urlsafe_id, nested_dict_get
from fastapi import APIRouter, Body, Depends, Query
from se_api_svc.repository.order.orders import OrdersRepository  # type: ignore[attr-defined]
from se_api_svc.schemas.orders.common import (  # type: ignore[attr-defined]
    GetOrdersAndExecutionsTimeIn,
    GetOrdersIn,
    OrdersTrendChart,
    TimeOperator,
    TimeType,
)
from se_api_svc.services.news_service import NewsEventQuery, NewsQuery
from se_api_svc.utils.order import (
    apply_aggregated_order_data,
    apply_day_trading_volume,
)
from se_elastic_schema.static.mifid2 import OrderStatus
from starlette.requests import Request
from starlette.responses import Response
from typing import List, Optional, Union

log = logging.getLogger(__name__)

router = APIRouter()


class NoContentResponse(Response):
    def __init__(self, msg=None):
        super().__init__(content=msg, status_code=http.HTTPStatus.NO_CONTENT)


class GetOrdersOrExecutionsOut(SearchResult):  # type: ignore[misc]
    results: List[Order]


async def _get_orders(
    repo: OrdersRepository,
    market_data_client: MasterDataClient = None,
    params: PaginatedDatedListParams = None,
    keys: Optional[List[str]] = None,
    orders_only: bool = False,
    include_day_trading_volume: bool = False,
    **search_body_params,
) -> RawResult:
    search_params = dict(
        hit_deserializer=lambda x: x["_source"],
        **search_body_params,
        **params.as_search_kwargs(as_model_qs=False),
    )

    if orders_only:
        result = await repo.get_orders_records(**search_params)
    else:
        if keys:
            result = await repo.get_orders_and_executions_by_keys(keys=keys, **search_params)
        else:
            result = await repo.get_orders_and_executions(**search_params)

    orders = result.as_list()

    aggregation_params = search_params.copy()
    aggregation_params.pop("hit_deserializer")
    aggregation_params.pop("pagination")
    aggregation_params.pop("search")
    if aggregation_params.get("f"):
        filter_str = aggregation_params["f"]
        pattern = (
            r"bestExecutionData\.orderVolume\.ecbRefRate\.[A-Za-z]+ "
            r"(?:ge|gte|le|lte|gt|lt|eq) "
            r"-?\d+(?:\.\d+)?\s*"
        )
        aggregation_params["f"] = re.sub(pattern, "", filter_str, count=1).strip()

    await apply_aggregated_order_data(repo=repo, orders=orders, **aggregation_params)

    if include_day_trading_volume and len(orders) <= 25:
        apply_day_trading_volume(market_data_client=market_data_client, orders=orders)

    return result


@router.get("", name="orders:get-orders", response_model=GetOrdersOrExecutionsOut)
@master_data_exception_handler
async def get_orders(
    params: PaginatedDatedListParams = Depends(),
    instrument_ids: Optional[List[str]] = Query(None, alias="instrumentIds"),
    asset_classes: Optional[List[str]] = Query(None, alias="assetClasses"),
    time_to_fill: Optional[int] = Query(None, alias="timeToFill"),
    internal_time_to_fill: Optional[int] = Query(None, alias="internalTimeToFill"),
    external_time_to_fill: Optional[int] = Query(None, alias="externalTimeToFill"),
    ecb_ref_rate: Optional[float] = Query(None, alias="ecbRefRate"),
    order_ids: Optional[List[str]] = Query(None, alias="orderIds"),
    execution_keys: Optional[List[str]] = Query(None, alias="executionKeys"),
    order_volume_native: Optional[float] = Query(None, alias="orderVolume"),
    include_day_trading_volume: Optional[bool] = Query(False, alias="includeDayTradingVolume"),
    repo: OrdersRepository = ReqDep(OrdersRepository),
    client: MasterDataClient = ReqDep(MasterDataClient),
    mb: FastApiMessageBus = Depends(),
):
    """Get orders & executions."""

    # exclusive_ecb_ref_rate is a param to restrict query only to Order model
    # exclusive_time_to_fill is a param to restrict query only to OrderState model
    exclusive_ecb_ref_rate, exclusive_time_to_fill = repo.get_exclusive_filter_params(
        ecb_ref_rate,
        order_volume_native,
        time_to_fill,
        external_time_to_fill,
        internal_time_to_fill,
        params.f,
    )

    filter_order_ids = (
        [b64decode_urlsafe_id(order_id, fallback=True) for order_id in order_ids]
        if order_ids
        else None
    )

    filter_execution_keys = (
        [b64decode_urlsafe_id(execution_key, fallback=True) for execution_key in execution_keys]
        if execution_keys
        else None
    )

    result = await _get_orders(
        repo=repo,
        market_data_client=client,
        params=params,
        orders_only=True,
        instrument_ids=instrument_ids,
        asset_classes=asset_classes,
        time_to_fill=time_to_fill,
        internal_time_to_fill=internal_time_to_fill,
        external_time_to_fill=external_time_to_fill,
        ecb_ref_rate=ecb_ref_rate,
        order_volume_native=order_volume_native,
        exclusive_ecb_ref_rate=exclusive_ecb_ref_rate,
        exclusive_time_to_fill=exclusive_time_to_fill,
        include_day_trading_volume=include_day_trading_volume,  # type: ignore[arg-type]
        filter_order_ids=filter_order_ids,
        filter_execution_keys=filter_execution_keys,
    )

    await mb.publish(OrdersSearched(search_text=params.search, refine_query=params.f))

    return SearchResult.from_raw_result(result, skipped_hits=params.page_params.skip)


@router.post(
    "", name="orders:post-orders-and-executions-search", response_model=GetOrdersOrExecutionsOut
)
async def retrieve_orders_and_executions(
    body: Union[List[str], Optional[GetOrdersIn]] = Body(None),
    params: PaginatedListParams = Depends(),
    repo: OrdersRepository = ReqDep(OrdersRepository),
    mb: FastApiMessageBus = Depends(),
):
    """Get orders & executions."""

    keys = None
    search_params = {}
    if body:
        # List is the old way
        if isinstance(body, list):
            keys = body
        elif isinstance(body, GetOrdersIn):
            keys = body.keys

            del body.keys
            search_params = body.dict()

    result = await _get_orders(repo=repo, params=params, keys=keys, **search_params)
    await mb.publish(OrdersSearched(search_text=params.search, refine_query=params.f))
    return SearchResult.from_raw_result(result, skipped_hits=params.page_params.skip)


@router.get(
    path="/{encoded_order_id}/executions",
    response_model=GetOrdersOrExecutionsOut,
    name="orders:get-executions-for-order",
    summary="Retrieve all Executions for a given Order ID",
    description="Retrieve all Executions for a given Order ID",
)
@router.get(
    path="/executions",
    response_model=GetOrdersOrExecutionsOut,
    name="orders:get-executions",
    summary="Retrieve all Executions",
    description="Retrieve all Executions",
)
async def get_order_executions(
    request: Request,
    encoded_order_id: Optional[str] = None,
    params: PaginatedDatedListParams = Depends(),
    repo: OrdersRepository = ReqDep(OrdersRepository),
    mb: FastApiMessageBus = Depends(),
) -> SearchResult:
    """Get executions by Order Id."""
    # Decode order ID if provided
    order_id = b64decode_urlsafe_id(encoded_order_id, fallback=True) if encoded_order_id else None

    # Apply default pagination only when fetching all executions
    if encoded_order_id is None:
        params.page_params = CustomPageParams(take=10_000)

    result = await repo.get_executions(order_id=order_id, **params.as_search_kwargs())
    await mb.publish(
        OrdersSearched(search_text=params.search, refine_query=params.f, request=request)
    )
    return SearchResult.from_raw_result(result, skipped_hits=params.page_params.skip)


@router.get(
    path="/{encoded_order_id}/tca-metrics/summary",
    name="orders:get-tca-metrics-for-order",
    summary="Retrieve summary of all TCA Metrics for a given Order ID",
    description="Retrieve summary of all TCA Metrics for a "
    "given Order ID for all PARF/FILL executions",
)
async def get_order_tca_metrics_summary(
    encoded_order_id: Optional[str] = None,
    params: NonPaginatedDatedListParams = Depends(),
    repo: OrdersRepository = ReqDep(OrdersRepository),
):
    """Get TCA Metrics Summary by Order Id."""
    order_id = b64decode_urlsafe_id(encoded_order_id, fallback=True)
    result = await repo.get_tca_metrics_summary(order_id=order_id, **params.as_search_kwargs())
    return result


@router.get(
    path="/{encoded_order_id}/news",
    name="orders:get-order-news",
    summary="Retrieve news for a single Order or Execution by ID",
    description="Retrieve news for a single Order or Execution by ID",
    dependencies=[Depends(ModulePermissionChecker(All(Module.CASE_MANAGER)))],
)
@master_data_exception_handler
async def get_single_order_news(
    encoded_order_id: str = None,
    news_params: NewsParams = Depends(),
    repo: OrdersRepository = ReqDep(OrdersRepository),
    master_data_news_service: MasterDataNewsService = ReqDep(MasterDataNewsService),
    mb: FastApiMessageBus = Depends(),
):
    order_id = b64decode_urlsafe_id(encoded_order_id, fallback=True)
    order = (await repo.get_order_or_execution(id_=order_id)).dict()

    sources = (
        [x for x in news_params.sources if x and len(x.strip()) != 0] if news_params.sources else []
    )

    event_queries: List[NewsEventQuery] = list()

    news_query = NewsQuery(
        sources=sources,
        relevance=news_params.relevance,
        number_of_articles=news_params.number_of_articles,
        queries=event_queries,
    )

    news_query.queries.append(
        NewsEventQuery(
            isin=nested_dict_get(order, "instrumentDetails.instrument.instrumentIdCode"),
            event_timestamp=nested_dict_get(order, "timestamps.orderSubmitted"),
            minutes_after=news_params.minutes_after,
            minutes_before=news_params.minutes_before,
        )
    )

    body = news_params.as_refinitiv_kwargs()
    instrumentIdCodesWithRange = news_query.timestamp_before_after
    articles = await master_data_news_service.get_stories(
        body=body, instrumentIdCodesWithRange=instrumentIdCodesWithRange
    )
    await mb.publish(RefinitivNewsSearched(order=order))
    return {"articles": articles}


@router.get(
    path="/{encoded_order_id}",
    name="orders:get-order",
    summary="Retrieve a single Order or Execution by ID",
    description="Retrieve a single Order or Execution by ID",
)
@master_data_exception_handler
async def get_single_order(
    request: Request,
    encoded_order_id: Optional[str] = None,
    repo: OrdersRepository = ReqDep(OrdersRepository),
    client: MasterDataClient = ReqDep(MasterDataClient),
    mb: FastApiMessageBus = Depends(),
):
    order_id = b64decode_urlsafe_id(encoded_order_id, fallback=True)
    order = await repo.get_order_or_execution(id_=order_id)

    await mb.publish(OrderViewed(order=order, request=request))

    order = order.dict(by_alias=True)
    apply_day_trading_volume(
        market_data_client=client,
        orders=await apply_aggregated_order_data(repo=repo, orders=[order]),
    )
    return order


@router.get(
    "/summary/by-status", description="Get order summary statistics grouped by order status."
)
async def get_orders_summary_by_status(
    params: DatedListParams = Depends(),
    counterparties: Optional[List[str]] = Query(default=None),
    people: Optional[List[str]] = Query(default=None),
    repo: OrdersRepository = ReqDep(OrdersRepository),
):
    return await repo.get_orders_summary_by_status(
        counterparties=counterparties, people=people, **params.as_search_kwargs()
    )


@router.get(
    "/summary/by-trend/{trend_chart}",
    description="Get order trend summary data for charting and visualization.",
)
async def get_orders_summary_by_trend(
    trend_chart: OrdersTrendChart,
    time_operator: Optional[TimeOperator] = Query(None, alias="timeOperator"),
    time_type: Optional[TimeType] = Query(None, alias="timeType"),
    time_end: Optional[int] = Query(None, ge=0, le=24 * 60 * 60 * 1000, alias="timeEnd"),
    time_start: Optional[int] = Query(None, ge=0, le=24 * 60 * 60 * 1000, alias="timeStart"),
    params: NonPaginatedDatedListParams = Depends(),
    repo: OrdersRepository = ReqDep(OrdersRepository),
):
    time_of_day = None
    if time_start and time_end and time_operator and time_type:
        time_of_day = GetOrdersAndExecutionsTimeIn(
            start=time_start, end=time_end, operator=time_operator, type=time_type
        )

    return await repo.get_orders_trends_summary_by_type(
        trend_chart=trend_chart,
        time_of_day=time_of_day,
        **params.as_search_kwargs(as_model_qs=False),
    )


@router.get(
    "/summary/by-time-and-status",
    description="Get order summary data grouped by time intervals and order status.",
)
async def get_orders_summary_by_time_and_status(
    params: NonPaginatedDatedListParams = Depends(),
    interval: Optional[TimeInterval] = None,
    buckets: Optional[int] = Query(None, ge=1, le=500),
    repo: OrdersRepository = ReqDep(OrdersRepository),
):
    if buckets is None:
        buckets = 100

    return await repo.get_orders_timeline(
        interval=interval,
        buckets=buckets,
        **params.as_search_kwargs(as_model_qs=False),
    )


@router.get("/summary/stats", description="Get comprehensive statistical summary of orders data.")
async def get_orders_summary_stats(
    params: DatedListParams = Depends(),
    counterparties: Optional[List[str]] = Query(default=None),
    people: Optional[List[str]] = Query(default=None),
    repo: OrdersRepository = ReqDep(OrdersRepository),
):
    return await repo.get_orders_stats(
        counterparties=counterparties, people=people, **params.as_search_kwargs()
    )


@router.get(
    path="/summary/by/{field}",
    name="orders:summary_by_field",
    summary="Get Orders and OrderAlerts data summary by field",
)
async def get_alerts_summary_by_field(
    field: str,
    params: NonPaginatedListParams = Depends(),
    take: Optional[int] = Query(50),
    orders_only: bool = Query(alias="ordersOnly", default=False),
    repo: OrdersRepository = ReqDep(OrdersRepository),
):
    # TODO: Currently, this function aggregates non-nested fields only.
    #       Need to enhance support for nested fields in the future.

    return await repo.get_orders_summary_by_field(
        agg_field=field,
        order_status=[OrderStatus.NEWO] if orders_only else None,
        **params.as_search_kwargs(as_model_qs=False),
        take=take,
    )
