# type: ignore
import binascii
import csv
import http
import logging
from api_sdk.auth import Tenancy, require_permissions
from api_sdk.config_base import ApiConfig
from api_sdk.di.request import ReqDep
from api_sdk.exceptions import (
    BadInput,
    BadRequest,
    MfaException,
    NotFound,
    PermissionsException,
)
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.permissions import Permission
from api_sdk.models.request_params import PaginatedListParams
from api_sdk.models.search import PageParams, SearchResult
from api_sdk.record_handler import RecordResponse
from api_sdk.responses import J<PERSON><PERSON><PERSON>ponse, OkResponse
from api_sdk.schemas.static import Module
from api_sdk.security.exceptions import UserException
from api_sdk.security.policy.engine import PolicyEngine
from api_sdk.services.kc.kc import KeyCloakService
from api_sdk.utils.database import delete_user_policy_look_up
from api_sdk.utils.utils import StringEnum, nested_dict_get, nested_get
from elasticsearch import ElasticsearchException  # pants: no-infer-dep
from elasticsearch8.exceptions import ConflictError
from enum import Enum, auto
from fastapi import (
    APIRouter,
    BackgroundTasks,
    Body,
    Depends,
    HTTPException,
    Query,
    status,
)
from io import StringIO
from pydantic.fields import Field
from pydantic.main import BaseModel
from se_api_svc.messages.account.command_handlers import (
    generate_user_crud_message,
    generate_user_pwd_reset_message,
    generate_user_session_message,
)
from se_api_svc.messages.account.commands import GetAccountUsersCommand
from se_api_svc.messages.account.events import (
    AccountUserChangedPassword,
    AccountUserCreated,
    AccountUserDeleted,
    AccountUserEmailNotification,
    AccountUserLockedStatusUpdated,
    AccountUserMFAActivated,
    AccountUserMFADeactivated,
    AccountUserMFAValidationFailure,
    AccountUserMFAValidationSuccess,
    AccountUserUpdated,
    AccountUserViewed,
    UserChange,
)
from se_api_svc.middleware.deps.kc import kc_denied_route
from se_api_svc.repository.account.users import UserRepository
from se_api_svc.repository.admin.roles import RolesRepo, UserRoleRepo
from se_api_svc.schemas.account import (
    AccountUser,
    AccountUserAction,
    AccountUserAdminTypes,
    AccountUserEmailPayload,
    AccountUserLockUnlockParam,
    AccountUserNotifyParam,
    AccountUserWithRoles,
    ListUsersResponse,
    UpdateUserIn,
    UserAttributes,
    UserDataAccessPolicy,
)
from se_api_svc.services.user_manager import UserManagerService
from se_elastic_schema.models.tenant.account.user_cred import UserCred
from starlette.requests import Request
from typing import List, Optional

router = APIRouter()

logger = logging.getLogger(__name__)
CRUD_UPDATES_EMAIL_RECEIPIENTS = ["<EMAIL>"]


def duplicate_modules(roles) -> str | None:
    modules = set()
    for role in roles:
        if role.module in modules:
            return role.module
        modules.add(role.module)


class NoopResponse(JSONResponse):
    def __init__(self, msg=None, **content):
        content.setdefault("status_code", http.HTTPStatus.NOT_MODIFIED)
        if msg is not None:
            content.setdefault("status", msg)
        super().__init__(content=content)


class UsersFormat(StringEnum):
    json = auto()
    csv = auto()


def _get_status(hit):
    try:
        if hit.activated:
            return "ACTIVE"
        else:
            return "INACTIVE"
    except AttributeError:
        return ""


def _format_users_as_csv(hits):
    fields = {
        "locked": "Locked",
        "name": "Name",
        "email": "Email",
        "userid": "User Id",
        "status": "Status",
        "roles": "Roles",
    }
    buffer = StringIO()
    writer = csv.DictWriter(buffer, fieldnames=list(fields.values()))
    writer.writeheader()
    for hit in hits:
        writer.writerow(
            {
                fields["locked"]: hit.locked if hit.locked else "",
                fields["name"]: hit.name,
                fields["email"]: hit.email,
                fields["userid"]: hit.userId,
                fields["status"]: _get_status(hit),
                fields["roles"]: ",".join(hit.roles),
            }
        )
    return buffer.getvalue()


def get_auth_headers(request: Request, repo: UserRepository) -> dict:
    headers = request.headers
    return dict(
        auth_header=headers.get("Authorization", "__none__ none"),
        proxy_user=headers.get("X-Proxy-User"),
        realm=repo.realm,
        remote_addr=headers.get("X-Forwarded-For"),
        session_token=headers.get("X-Session-Token"),
        email=headers.get("X-Email") or headers.get("x-email"),
    )


async def get_user_by_id_or_email(user_identifier: str, repo: UserRepository) -> AccountUser:
    try:
        user: AccountUser = await repo.get_one(id=user_identifier, record_model=AccountUser)
    except NotFound:
        user: AccountUser = await repo.get_user_by_email(email=user_identifier)

    if not user:
        raise NotFound(f"AccountUser with id {user_identifier} does not exist.")

    user_creds: Optional[List[UserCred]] = await repo.get_user_cred_by_user_ids(user_id=user.userId)

    if user_creds and user_creds[0].locked is not None:
        user.locked = user_creds[0].locked

    return user


def formatted_value(value) -> str:
    def to_str(item):
        if isinstance(item, Enum):
            return str(item.value).replace("_", " ").title()
        return str(item)

    if isinstance(value, list):
        return str([to_str(v) for v in value])
    elif value is None:
        return ""

    return to_str(value)


def get_account_user_changes(old: AccountUser, new: AccountUser):
    changes = []
    ignore_fields = (
        "hash_",
        "id_",
        "traitFqn_",
        "parent_",
        "key_",
        "model_",
        "timestamp_",
        "user_",
        "validationErrors_",
        "cascadeId_",
        "version_",
        "link_",
        "uniqueProps_",
        "&ancestor",
    )
    for key in vars(new):
        if key not in ignore_fields and getattr(old, key) != getattr(new, key):
            changes.append(
                UserChange(
                    field=key,
                    old_value=formatted_value(getattr(old, key)),
                    new_value=formatted_value(getattr(new, key)),
                )
            )

    return changes


@router.get("/emails", name="account:users:get-user-emails", response_model=ListUsersResponse)
async def get_user_emails(
    page_params: PageParams = Depends(),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
):
    """List activated user emails."""
    try:
        repo.tenancy.require_permissions(Permission.ADMIN)
        users = await repo.get_users(pagination=page_params.to_pagination(), activated="true")
    except PermissionsException:
        users = await repo.get_users(
            pagination=page_params.to_pagination(),
            activated="true",
            user_ids=[repo.tenancy.userId],
        )

    return SearchResult.from_raw_result(users, skipped_hits=page_params.skip)


@router.get(
    "/admins/emails",
    name="account:users:get-admin-emails",
    response_model=ListUsersResponse,
)
@require_permissions(permissions=[Permission.ADMIN])
async def get_admin_emails(
    adminType: Optional[AccountUserAdminTypes] = None,
    page_params: PageParams = Depends(),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
):
    """List activated admin user emails."""
    users = await repo.get_admin_users(
        pagination=page_params.to_pagination(), activated="true", admin_type=adminType
    )
    return SearchResult.from_raw_result(users, skipped_hits=page_params.skip)


@router.get("/case-managers", name="account:users:list-case-managers")
@require_permissions(permissions=[Permission.CASE_MANAGER])
async def get_case_manager_users(
    page_params: PageParams = Depends(),
    search: Optional[str] = Query(None),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
):
    """List activated Case Manager users."""
    users = await repo.get_case_manager_users(
        pagination=page_params.to_pagination(), activated="true", search=search
    )
    return SearchResult.from_raw_result(users, skipped_hits=page_params.skip)


@router.get("", name="account:users:list")
async def get_users(
    params: PaginatedListParams = Depends(),
    mb: FastApiMessageBus = Depends(),
    role_ids: Optional[List[str]] = Query(None, alias="roleId"),
):
    """List account users.

    Parameters
    ----------
    f (str): Refine filter string
    search (str): Search string

    Returns
    -------
    dict:
        header (dict): pagination header
        results (List[dict]): records
    """

    resp = await mb.request(GetAccountUsersCommand(params=params, role_ids=role_ids))

    return resp.result


@router.post("", name="account:users:create")
@require_permissions(permissions=[Permission.ADMIN], skip_aws_proxy=True)
async def create_user(
    # Make sure that the permission field in the input is from api_sdk.schemas.static.Module
    user: AccountUserWithRoles,
    request: Request,
    notify: Optional[AccountUserNotifyParam] = AccountUserNotifyParam.YES,
    user_manager: UserManagerService = ReqDep(UserManagerService, skip_policy=True),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    roles_repo: RolesRepo = ReqDep(RolesRepo, skip_policy=True),
    mb: FastApiMessageBus = Depends(),
    tenancy: Tenancy = ReqDep(Tenancy),
    background_tasks: BackgroundTasks = BackgroundTasks(),
):
    """Create user account if not already present.

    Parameters
    ----------
    user (AccountUser): user object

    Returns
    -------
    Success response:
    dict:
        id (str): user id
        index (str): user doc index
        key (str): user key
        status (WorkAction): user creation status
        versioon (str): user version

    Error response:
    dict:
        detail (List[dict]):
            msg (str): error message
    """
    roles = user.user_roles
    db_roles = None

    if nested_get(user, "surveillanceWorkflow.cSurv", default=None):
        user.surveillanceWorkflow.cSurv.appliedBy = repo.tenancy.userId

    if nested_get(user, "surveillanceWorkflow.tSurv", default=None):
        user.surveillanceWorkflow.tSurv.appliedBy = repo.tenancy.userId

    for field in UserDataAccessPolicy.__fields__:
        policy_detail = nested_get(user.appliedPolicy, f"{field}", default=None)
        if policy_detail:
            policy_detail.appliedBy = repo.tenancy.userId

    if roles:
        db_roles = roles_repo.get_roles_by_id(roles)
        # Make sure that all roles are of different modules
        duplicate = duplicate_modules(db_roles)
        if duplicate:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"More than one role of module {duplicate} found",
            )
        if len(db_roles) != len(roles):
            logger.info("one of the roles not found")
            logger.info(f"roles: {roles}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="some roles not found",
            )

    user.sanitize_data()
    try:
        response: RecordResponse = user_manager.create_user(
            user=AccountUser.parse_obj(user).dict(by_alias=True),
            create_temp_session=False,
            **get_auth_headers(request=request, repo=repo),
            kc_enabled=tenancy.kc_enabled,
        )
        # Create a temporary session manually so that session.token can be used to send email
        updated_user: AccountUser = await repo.get_one(id=response.id, record_model=AccountUser)
        session = request.app.auth_manager.save_temp_session(
            acc_user=updated_user.dict(by_alias=True),
            realm=repo.realm,
            email=updated_user.email,
        )

        # Handle Roles
        if roles and db_roles:
            # ToDo: Add check if user has permission to attach roles
            roles_repo.add_roles(db_roles, response.id)

        # Handle email notification
        if notify == AccountUserNotifyParam.YES:
            # Generate email message and send activation email
            email_message = generate_user_session_message(
                realm=repo.realm, name=user.name, token=session.token
            )
            await mb.publish(
                AccountUserEmailNotification(message=email_message, recipients=[updated_user.email])
            )

            # if ".dev." not in repo.realm:
            # Send CRUD update email
            crud_email_message = generate_user_crud_message(
                realm=repo.realm,
                action=AccountUserAction.CREATED,
                email=user.email,
                user=repo.tenancy.user_name,
            )
            await mb.publish(
                AccountUserEmailNotification(
                    message=crud_email_message,
                    recipients=CRUD_UPDATES_EMAIL_RECEIPIENTS,
                )
            )

        if user.appliedPolicy:
            temp_policy_engine = PolicyEngine(
                client=repo.repo.es_repo.client, db=repo.repo.db, index=None, request_module=None
            )

            background_tasks.add_task(
                temp_policy_engine.apply_user_policy_async,
                session=None,
                tenant=repo.repo.tenant,
                user_id=user.userId,
                apply_full=True,
            )

        # UserAudit Event
        await mb.publish(AccountUserCreated(user=updated_user, request=request))

        return OkResponse(**response._asdict())
    except ElasticsearchException as e:
        raise HTTPException(status_code=400, detail=nested_dict_get(e.info, "error.reason"))
    except (UserException, ConflictError):
        raise HTTPException(status_code=400, detail=f"AccountUser:{user['userId']} already exists")


@router.put("/forgot-pwd", name="account:users:forgot-password")
async def forgot_password(
    body: AccountUserEmailPayload,
    request: Request,
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    mb: FastApiMessageBus = Depends(),
    tenancy: Tenancy = ReqDep(Tenancy),
    service: KeyCloakService = ReqDep(KeyCloakService),
):
    """Generate temporary session and send forgot password email."""
    user: AccountUser = await repo.get_user_by_email(email=body.email)
    if not user:
        # added fake request to equalize time with request time if request succeed
        await repo.get_user_by_email(email=body.email)
        return OkResponse(sent=True)

    if tenancy.kc_enabled:
        service.send_password_reset_mail(
            email=body.email, realm=tenancy.tenant, tenant_realm=tenancy.realm
        )
    else:
        try:
            # Generate a temporary session for the user.
            session = request.app.auth_manager.save_temp_session(
                acc_user=user.dict(by_alias=True), realm=repo.realm, email=user.email
            )

            # Generate email message and send password reset email.
            email_message = generate_user_pwd_reset_message(
                realm=repo.realm, name=user.name, token=session.token
            )
            await mb.publish(
                AccountUserEmailNotification(message=email_message, recipients=[user.email])
            )
        except ElasticsearchException as e:
            raise BadInput(nested_dict_get(e.info, "error.reason"))

    return OkResponse(sent=True)


@router.get(
    "/_self_",
    name="account:users:validate",
    description="Validate and retrieve current user's account details and attributes.",
)
async def user_account_validate(
    request: Request,
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    attribute: Optional[List[UserAttributes]] = Query(None),
    mb: FastApiMessageBus = Depends(),
):
    # Note: Do not apply policy if user is trying to fetch it's own data
    repo.repo.skip_policy = True
    user: AccountUser = await repo.get_user_by_email(repo.tenancy.session.principal)
    user = await repo.append_user_attributes(user=user.dict(), attribute=attribute)
    # UserAudit Event
    await mb.publish(AccountUserViewed(user=user, request=request))

    return user


@router.get("/{user_identifier}", name="account:user:get")
@require_permissions(permissions=[Permission.ADMIN], skip_aws_proxy=True, skip_on_same_user=True)
async def get_user(
    user_identifier: str,
    request: Request,
    mb: FastApiMessageBus = Depends(),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    user_roles_repo: UserRoleRepo = ReqDep(UserRoleRepo),
):
    """Get account user details by user id first. If not found, find by email
    else throw error.

    Parameters
    ----------
    user_identifier (str): userId or email

    Returns
    -------
    dict: user object
    """
    user: AccountUser = await get_user_by_id_or_email(user_identifier=user_identifier, repo=repo)

    # UserAudit Event
    await mb.publish(AccountUserViewed(user=user, request=request))

    result = user.to_dict()

    result["user_roles"] = await user_roles_repo.get_users_with_roles(user_ids=[user.userId])

    return result


@router.put("/{user_id}", name="account:users:edit")
@require_permissions(
    permissions=[Permission.ADMIN],
    skip_aws_proxy=True,
    allow_inactive_user=True,
    record_model=AccountUser,
    user_property="user_id",
)
async def edit_user(
    user_id: str,
    user: UpdateUserIn,
    request: Request,
    mb: FastApiMessageBus = Depends(),
    user_manager: UserManagerService = ReqDep(UserManagerService),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    tenancy: Tenancy = ReqDep(Tenancy),
    kc: KeyCloakService = ReqDep(KeyCloakService),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    roles: RolesRepo = ReqDep(RolesRepo),
):
    """Edit the account user details.

    Parameters
    ----------
    user_id (str): user id
    user (AccountUser): user object
    Returns
    -------
    Success response:
    dict:
        id (str): user id
        index (str): user doc index
        key (str): user key
        status (WorkAction): user deletion status
        versioon (str): user version
    Error response:
    dict:
        detail (List[dict]):
            msg (str): error message
    """

    if nested_get(user, "surveillanceWorkflow.cSurv", default=None):
        user.surveillanceWorkflow.cSurv.appliedBy = repo.tenancy.userId

    if nested_get(user, "surveillanceWorkflow.tSurv", default=None):
        user.surveillanceWorkflow.tSurv.appliedBy = repo.tenancy.userId

    for field in UserDataAccessPolicy.__fields__:
        policy_detail = nested_get(user.appliedPolicy, f"{field}", default=None)
        if policy_detail:
            policy_detail.appliedBy = repo.tenancy.userId

    user.sanitize_data()

    try:
        original_user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)

        if original_user.mfaStatus != user.mfaStatus:
            if user.mfaStatus == AccountUser.MfaStatus.ACTIVATED:
                raise HTTPException(
                    detail="MFA status can't be set to ACTIVATED by user",
                    status_code=status.HTTP_400_BAD_REQUEST,
                )

            if all(
                [
                    # Mfa is changed, and been set to NOT_SET
                    user.mfaStatus == AccountUser.MfaStatus.NOT_SET,
                    tenancy.kc_enabled,
                ]
            ):
                # If MFA status changed, and kc is enabled, NOT_SET status should
                # be changed to ACTIVATED, because, the registration of MFA will
                # be done via KC email, and not by SE front-end.
                logger.info("Setting MFA to ACTIVATED since, keycloak is enabled")
                user.mfaStatus = AccountUser.MfaStatus.ACTIVATED

        response: RecordResponse = user_manager.update_user(
            user=user.dict(by_alias=True),
            **get_auth_headers(request=request, repo=repo),
            kc_enabled=tenancy.kc_enabled,
        )

        # UserAudit Event
        updated_user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)
        changes = get_account_user_changes(original_user, updated_user)
        await mb.publish(
            AccountUserUpdated(
                user=updated_user,
                request=request,
                key_before=user.key_,
                changes=changes,
            )
        )

        # If permissions updated, log user out.
        if set(original_user.permissions) != set(updated_user.permissions):
            request.app.auth_manager.logout_sessions(session=tenancy, user_id=user_id)

            # Remove roles if user made admin.
            if (
                Module.ADMIN in updated_user.permissions
                and Module.ADMIN not in original_user.permissions
            ):
                roles.reset_roles(original_user.userId, [])

        if original_user.mfaStatus != user.mfaStatus:
            if tenancy.kc_enabled:
                if user.mfaStatus == AccountUser.MfaStatus.ACTIVATED:
                    logger.debug("Enabling KC MFA")
                    kc.enable_otp(user.email, tenancy.tenant)
                if user.mfaStatus == AccountUser.MfaStatus.DISABLED:
                    logger.debug("Disabling KC MFA")
                    # Delete the OTPs first.
                    kc.disable_otp(user.email, tenancy.tenant)
            else:
                logger.debug("Sending session email")
                session = request.app.auth_manager.save_temp_session(
                    acc_user=user.dict(by_alias=True), realm=repo.realm, email=original_user.email
                )

                email_message = generate_user_session_message(
                    realm=repo.realm, name=user.name, token=session.token
                )
                await mb.publish(
                    AccountUserEmailNotification(message=email_message, recipients=[user.email])
                )
        delete_user_policy_look_up(db=repo.repo.db, tenant=repo.repo.tenant, user_ids=[user_id])

        temp_policy_engine = PolicyEngine(
            client=repo.repo.es_repo.client, db=repo.repo.db, index=None, request_module=None
        )

        background_tasks.add_task(
            temp_policy_engine.apply_user_policy_async,
            session=None,
            tenant=repo.repo.tenant,
            user_id=user_id,
            apply_full=True,
        )

        return OkResponse(**response._asdict())
    except ElasticsearchException as e:
        raise BadInput(nested_dict_get(e.info, "error.reason"))


@router.put("/{user_identifier}/resend-activation", name="account:users:resend-activation")
@require_permissions(
    permissions=[Permission.ADMIN],
    skip_aws_proxy=True,
    allow_inactive_user=True,
    record_model=AccountUser,
)
async def resend_user_activation(
    user_identifier: str,
    request: Request,
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    mb: FastApiMessageBus = Depends(),
    tenancy: Tenancy = ReqDep(Tenancy),
    service: KeyCloakService = ReqDep(KeyCloakService),
):
    """Generate temporary user session and re-send activation link.

    :param user_identifier (str): userId or email
    """
    user: AccountUser = await get_user_by_id_or_email(user_identifier=user_identifier, repo=repo)

    if tenancy.kc_enabled:
        service.send_password_reset_mail(
            email=user.email, realm=tenancy.tenant, tenant_realm=tenancy.realm
        )
    else:
        try:
            # Create a temporary session manually so that session.token can be used to send email
            session = request.app.auth_manager.save_temp_session(
                acc_user=user.dict(by_alias=True), realm=repo.realm, email=user.email
            )

            # Generate email message and resend activation email
            email_message = generate_user_session_message(
                realm=repo.realm, name=user.name, token=session.token
            )
            await mb.publish(
                AccountUserEmailNotification(message=email_message, recipients=[user.email])
            )
        except ElasticsearchException as e:
            raise BadInput(nested_dict_get(e.info, "error.reason"))

    return OkResponse(resend=True)


@router.delete("/{user_id}", name="account:users:delete")
@require_permissions(permissions=[Permission.ADMIN], skip_aws_proxy=True)
async def delete_user(
    user_id: str,
    request: Request,
    notify: Optional[AccountUserNotifyParam] = AccountUserNotifyParam.YES,
    user_manager: UserManagerService = ReqDep(UserManagerService),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    mb: FastApiMessageBus = Depends(),
    roles_repo: RolesRepo = ReqDep(RolesRepo),
    tenancy: Tenancy = ReqDep(Tenancy),
):
    """Delete the account user.

    Parameters
    ----------
    user_id (str): user id

    Returns
    -------
    Success response:
    dict:
        id (str): user id
        index (str): user doc index
        key (str): user key
        status (WorkAction): user updation status
        version (str): user version
    Error response:
    dict:
        detail (List[dict]):
            msg (str): error message
    """
    user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)
    try:
        response: RecordResponse = user_manager.delete_user(
            user_id=user_id,
            **get_auth_headers(request=request, repo=repo),
            kc_enabled=tenancy.kc_enabled,
        )

        # Send CRUD update email
        if notify == AccountUserNotifyParam.YES and ".dev." not in repo.realm:
            crud_email_message = generate_user_crud_message(
                realm=repo.realm,
                action=AccountUserAction.DELETED,
                email=user.email,
                user=repo.tenancy.user_name,
            )
            await mb.publish(
                AccountUserEmailNotification(
                    message=crud_email_message,
                    recipients=CRUD_UPDATES_EMAIL_RECEIPIENTS,
                )
            )

        # UserAudit Event
        await mb.publish(AccountUserDeleted(user=user, request=request))

        # Delete roles assigned. This should have been done with HookExecution,
        # but the deletion is handled directly via RecordHandler,
        # and not via repository.
        roles_repo.delete_user(user_id)

        return OkResponse(**response._asdict())
    except ElasticsearchException as e:
        raise BadInput(nested_dict_get(e.info, "error.reason"))


@router.post(
    "/{user_id}/password",
    name="account:users:set-password",
    dependencies=[Depends(kc_denied_route)],
)
async def set_user_password(
    user_id: str,
    request: Request,
    user_manager: UserManagerService = ReqDep(UserManagerService, skip_policy=True),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    mb: FastApiMessageBus = Depends(),
):
    """Change the user's password.

    :param user_id: str the user's ID to change the password of
    """
    password = await request.body()

    try:
        user_manager.set_password(
            user_id=user_id,
            encrypted=password,
            **get_auth_headers(request=request, repo=repo),
        )
        # user audit event
        repo.repo.skip_policy = (
            True  # Skip applying policy as the user is still using unverified token
        )
        user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)
        await mb.publish(
            AccountUserChangedPassword(user=user, request=request, key_before=user.key_)
        )

        return OkResponse()
    except ElasticsearchException as e:
        raise BadInput(nested_dict_get(e.info, "error.reason"))
    except UserException as e:
        return NoopResponse(msg=e.args[0])


@router.post("/{user_id}/roles", description="Assign roles to a user account.")
async def add_roles(
    user_id: str,
    roles_in: List[str] = Body(..., description="Roles to attach to user"),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    roles_repo: RolesRepo = ReqDep(RolesRepo, skip_policy=True),
):
    await repo.get_one(id=user_id, record_model=AccountUser)
    roles = roles_repo.get_roles_by_id(roles_in)

    # Make sure that all roles are of different modules
    duplicate = duplicate_modules(roles)
    if duplicate:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"More than one role of module {duplicate} found",
        )
    # ToDo: Add check if user has permission to attach roles
    roles_repo.add_roles(roles, user_id)

    # ToDo: Add audit
    return OkResponse()


@router.post(
    "/{user_identifier}/mfa",
    name="account:users:register-mfa",
    description="Enroll the user for MFA",
    dependencies=[Depends(kc_denied_route)],
)
@require_permissions(permissions=[Permission.ADMIN], skip_on_same_user=True)
async def register_user_mfa(
    user_identifier: str,
    request: Request,
    user_manager: UserManagerService = ReqDep(UserManagerService, skip_policy=True),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    config: ApiConfig = ReqDep(ApiConfig, skip_policy=True),
    mb: FastApiMessageBus = Depends(),
):
    """
    Enroll the user for MFA, i.e. set mfa secret, and send the secret details
    to front-end, should not be used with keycloak.
    """
    repo.repo.skip_policy = True  # Skip applying policy as the user is still using unverified token
    user: AccountUser = await repo.get_one(id=user_identifier, record_model=AccountUser)
    await mb.publish(AccountUserMFAActivated(user=user, request=request, key_before=user.key_))

    try:
        resp = user_manager.register_mfa(
            user_id=user_identifier,
            environment=config.ENVIRONMENT,
            **get_auth_headers(request=request, repo=repo),
        )
        user.mfaStatus = AccountUser.MfaStatus.ACTIVATED
        await repo.save_existing(user)
        return resp
    except ElasticsearchException as e:
        raise BadInput(nested_dict_get(e.info, "error.reason"))


@router.delete(
    "/{user_id}/mfa",
    name="account:users:deactivate-mfa",
    description="Deactivate multi-factor authentication for a user account.",
)
@require_permissions(permissions=[Permission.ADMIN], skip_aws_proxy=True)
async def deactivate_user_mfa(
    user_id: str,
    request: Request,
    user_manager: UserManagerService = ReqDep(UserManagerService, skip_policy=True),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    mb: FastApiMessageBus = Depends(),
    tenancy: Tenancy = ReqDep(Tenancy, skip_policy=True),
    kc: KeyCloakService = ReqDep(KeyCloakService, skip_policy=True),
):
    """Deroll the user's MFA."""
    user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)

    try:
        if tenancy.kc_enabled:
            kc.enable_otp(user.email, tenancy.tenant)
            # For kc, we have to set mfa activated once the email by KC is sent.
            user.mfaStatus = AccountUser.MfaStatus.ACTIVATED
            await repo.save_existing(user)
        else:
            user_manager.deactivate_mfa(
                user_id=user_id, **get_auth_headers(request=request, repo=repo)
            )

            # Generate a temporary session for the user.
            session = request.app.auth_manager.save_temp_session(
                acc_user=user.dict(by_alias=True), realm=repo.realm, email=user.email
            )

            # Generate email message and resend activation email
            email_message = generate_user_session_message(
                realm=repo.realm, name=user.name, token=session.token
            )

            await mb.publish(
                AccountUserEmailNotification(message=email_message, recipients=[user.email])
            )

            user.mfaStatus = AccountUser.MfaStatus.NOT_SET
            await repo.save_existing(user)

        await mb.publish(
            AccountUserMFADeactivated(user=user, request=request, key_before=user.key_)
        )

        await mb.publish(
            AccountUserMFADeactivated(user=user, request=request, key_before=user.key_)
        )

        return OkResponse()
    except ElasticsearchException as e:
        raise BadInput(nested_dict_get(e.info, "error.reason"))


@router.put(
    "/{user_id}/mfa",
    name="account:users:validate-mfa",
    dependencies=[Depends(kc_denied_route)],
    description="Validate user's MFA code and activate the account.",
)
async def validate_user_mfa(
    user_id: str,
    request: Request,
    user_manager: UserManagerService = ReqDep(UserManagerService, skip_policy=True),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    mb: FastApiMessageBus = Depends(),
):
    """Validate the user's MFA code."""
    repo.repo.skip_policy = True  # Skip applying policy as the user is still using unverified token
    user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)
    otp = (await request.body()).decode("utf-8").strip()

    try:
        if user_manager.validate_mfa(
            user_id=user_id,
            otp=otp,
            **get_auth_headers(request=request, repo=repo),
        ):
            # Activate user when MFA is validated.
            user.activated = True
            user.mfaStatus = AccountUser.MfaStatus.ACTIVATED
            await repo.save_existing(user)

            await mb.publish(
                AccountUserMFAValidationSuccess(user=user, request=request, key_before=user.key_)
            )
            return OkResponse()
        await mb.publish(
            AccountUserMFAValidationFailure(user=user, request=request, key_before=user.key_)
        )
        raise MfaException("mfa validation failed")
    except ElasticsearchException as e:
        raise BadInput(nested_dict_get(e.info, "error.reason"))
    except binascii.Error:
        raise BadRequest("User's MFA isn't activated.")


@router.post(
    "/{user_identifier}/{lock_status}",
    name="account:users:lock-unlock",
    description="Lock or unlock a user account to prevent or allow access.",
)
@require_permissions(permissions=[Permission.ADMIN], skip_aws_proxy=True)
async def lock_unlock_user(
    user_identifier: str,
    lock_status: AccountUserLockUnlockParam,
    request: Request,
    mb: FastApiMessageBus = Depends(),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    user_manager: UserManagerService = ReqDep(UserManagerService, skip_policy=True),
    tenancy: Tenancy = ReqDep(Tenancy, skip_policy=True),
):
    """(Un)lock user's account.

    :param user_identifier: str the user's ID/Email to (un)lock the account of
    :param lock_status: AccountUserLockUnlockParam enum('lock', 'unlock') to whether lock or unlock
     account.
    """
    locked: bool = lock_status == AccountUserLockUnlockParam.LOCK
    auth_headers = get_auth_headers(request=request, repo=repo)
    user: AccountUser = await get_user_by_id_or_email(user_identifier=user_identifier, repo=repo)

    # Update auth lock / unlock status
    if locked:
        request.app.auth_manager.lock_account(user_id=user.userId, **auth_headers)

        # Logout all sessions, passing `tenancy` as session is hack here, to bypass the AWS
        # proxy. Since, `logout_sessions` only access the `.realm` property, the `tenancy` dep
        # provides it and thus bypassing the AWS proxy, by logging-out all sessions for the user
        # in ask
        request.app.auth_manager.logout_sessions(session=tenancy, user_id=user_identifier)
    else:
        request.app.auth_manager.unlock_account(
            user_id=user.userId,
            **auth_headers,
            kc_enabled=tenancy.kc_enabled,
        )

    # Set locked status
    user: AccountUser = await repo.get_one(id=user.userId, record_model=AccountUser)
    user.locked = locked

    try:
        # Update user
        response: RecordResponse = user_manager.update_user(
            user=user.dict(by_alias=True), **auth_headers, kc_enabled=tenancy.kc_enabled
        )

        # UserAudit Event
        updated_user: AccountUser = await repo.get_one(id=user.userId, record_model=AccountUser)
        await mb.publish(
            AccountUserLockedStatusUpdated(user=updated_user, request=request, key_before=user.key_)
        )

        return OkResponse(**response._asdict())
    except ElasticsearchException as e:
        raise BadInput(nested_dict_get(e.info, "error.reason"))


@router.put(
    "/{user_id}/{permission}",
    name="account:users:add-permission",
    description="Add a specific permission to a user account.",
)
@require_permissions(
    permissions=[Permission.ADMIN],
    skip_aws_proxy=True,
    allow_inactive_user=False,
    record_model=AccountUser,
)
async def add_user_permission(
    user_id: str,
    permission: Permission,
    request: Request,
    mb: FastApiMessageBus = Depends(),
    user_manager: UserManagerService = ReqDep(UserManagerService),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
):
    try:
        original_user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)

        if permission in original_user.permissions:
            # user already has this perm, stop
            return NoopResponse()

        original_user.permissions.append(permission)

        response: RecordResponse = user_manager.update_user(
            user=original_user.dict(by_alias=True),
            **get_auth_headers(request=request, repo=repo),
        )

        # UserAudit Event
        updated_user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)
        changes = get_account_user_changes(original_user, updated_user)
        await mb.publish(
            AccountUserUpdated(
                user=updated_user,
                request=request,
                key_before=original_user.key_,
                changes=changes,
            )
        )

        return OkResponse(**response._asdict())
    except ElasticsearchException as e:
        raise BadInput(nested_dict_get(e.info, "error.reason"))


@router.delete(
    "/{user_id}/{permission}",
    name="account:users:delete-permission",
    description="Remove a specific permission from a user account.",
)
@require_permissions(
    permissions=[Permission.ADMIN],
    skip_aws_proxy=True,
    allow_inactive_user=False,
    record_model=AccountUser,
)
async def delete_user_permission(
    user_id: str,
    permission: Permission,
    request: Request,
    mb: FastApiMessageBus = Depends(),
    user_manager: UserManagerService = ReqDep(UserManagerService),
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
):
    try:
        original_user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)

        if permission not in original_user.permissions:
            # user already has this perm, stop
            return NoopResponse()

        original_user.permissions = [
            perm for perm in original_user.permissions if perm != permission
        ]

        response: RecordResponse = user_manager.update_user(
            user=original_user.dict(by_alias=True),
            **get_auth_headers(request=request, repo=repo),
        )

        # UserAudit Event
        updated_user: AccountUser = await repo.get_one(id=user_id, record_model=AccountUser)
        changes = get_account_user_changes(original_user, updated_user)
        await mb.publish(
            AccountUserUpdated(
                user=updated_user,
                request=request,
                key_before=original_user.key_,
                changes=changes,
            )
        )

        return OkResponse(**response._asdict())
    except ElasticsearchException as e:
        raise BadInput(nested_dict_get(e.info, "error.reason"))


class ActivateUserIn(BaseModel):
    email: str = Field(..., description="Email of the user to activate.")


@router.post(
    "/activate",
    name="account:kc:activate",
    description="Activate a user account after KeyCloak required actions are completed.",
)
async def kc_activate(
    user: ActivateUserIn,
    repo: UserRepository = ReqDep(UserRepository, skip_policy=True),
    tenancy: Tenancy = ReqDep(Tenancy),
    service: KeyCloakService = ReqDep(KeyCloakService),
    user_manager: UserManagerService = ReqDep(UserManagerService, skip_policy=True),
):
    """Check if all the pending actions are done in KC, if yes, set the user as activated."""

    if not tenancy.kc_enabled:
        raise BadRequest("Keycloak is not enabled for this tenant.")

    user: AccountUser = await get_user_by_id_or_email(user_identifier=user.email, repo=repo)

    kc_user = service._get_kc_user_by_email(email=user.email, realm=tenancy.tenant)

    if kc_user and not kc_user.requiredActions:
        user.activated = True
        user_manager.update_user_without_permission_check(
            user=user.dict(by_alias=True),
            tenant=tenancy.tenant,
        )

    return OkResponse(resend=True)
