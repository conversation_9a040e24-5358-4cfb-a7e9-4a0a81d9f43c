# type: ignore
from api_sdk.di.request import ReqDep
from api_sdk.messages.fastapi_message_bus import FastApiMessageBus
from api_sdk.models.permissions import Permission
from api_sdk.models.request_params import PaginatedListParams
from api_sdk.models.search import SearchResult
from api_sdk.repository.asyncronous.request_bound import RequestBoundRepository
from api_sdk.responses import OkResponse
from api_sdk.utils.utils import nested_get
from fastapi import APIRouter, Body, Depends, Request
from fastapi.exceptions import HTTPException
from se_api_svc.api.routes import route_tags
from se_api_svc.messages.identifiers.commands import (
    AssignIdentifierCommand,
    AssignPersonIdentifiersCommand,
)
from se_api_svc.repository.comms_surveillance.info_barrier.projects import (
    InfoBarrierProjectRepository,
)
from se_api_svc.repository.market.market import MarketRepository
from se_api_svc.schemas.account import Account<PERSON>erson
from se_api_svc.schemas.identifiers import Identifier, IdentifierIn, PersonIdentifiersIn
from se_api_svc.schemas.person import PersonSummary
from se_api_svc.utils.crud_template import CRUD_CREATE, CRUD_DELETE, CRUD_UPDATE, add_crud_endpoints
from typing import List, Optional, Union


class ListPeopleSummaryResponse(SearchResult):
    results: List[PersonSummary]


router = APIRouter()


@router.get(
    "",
    name="account:people:get-all",
    response_model=ListPeopleSummaryResponse,
    tags=[route_tags.CRUD_PORT_MAY_2021],
)
async def get_people(
    params: PaginatedListParams = Depends(),
    repo: MarketRepository = ReqDep(MarketRepository),
):
    """
    List employees - AccountPerson records.
    """
    repo.tenancy.require_permissions(Permission.TRANSACTION_REPORTING | Permission.MARKET)
    emp_summary = await repo.get_employees_summary(
        **params.as_search_kwargs(as_model_qs=False), default_sort=["name:asc"]
    )
    return SearchResult.from_raw_result(emp_summary)


@router.get(
    "/{id}",
    name="account:people:get-one",
    tags=[route_tags.CRUD_PORT_MAY_2021],
    description="Get detailed information for a specific employee account person.",
    # DO NOT ADD response_model=AccountPerson as it would hide phone number's isValid
)
async def get_market_person(
    id: str,
    timestamp: Optional[int] = None,
    repo: RequestBoundRepository = ReqDep(RequestBoundRepository),
):
    repo.tenancy.require_permissions(Permission.MARKET)
    result = await repo.get_one(AccountPerson, id, timestamp=timestamp)
    return result.dict(by_alias=True, is_valid=True)


@router.delete(
    "/{id}",
    name="delete:account:people",
    tags=[route_tags.CRUD_PORT_MAY_2021],
    description="Delete an employee account person and handle info barrier project associations.",
)
async def delete_account_person(
    id: str,
    repo: RequestBoundRepository = ReqDep(RequestBoundRepository),
    info_barrier_repo: InfoBarrierProjectRepository = ReqDep(InfoBarrierProjectRepository),
):
    account_person = await repo.get_one(AccountPerson, id)
    info_barrier_project_ids = getattr(account_person, "infoBarrierProjectIds", None)
    # If the AccountPerson is associated with any InfoBarrierProjects.
    # Validate if the employee can be removed from the specified projects.
    if info_barrier_project_ids:
        employee_name = nested_get(account_person, "name")
        (
            _,
            _,
            non_removable_from_project_ids,
            non_removable_from_projects_names,
        ) = await info_barrier_repo.validate_project_removals(info_barrier_project_ids)
        # If there are any projects the employee cannot be removed from, raise an exception.
        if non_removable_from_project_ids:
            raise HTTPException(
                status_code=400,
                detail=f"The '{employee_name}' can not be unlinked from the projects {non_removable_from_projects_names}. Please try removing the employee directly from Info Barrier Project(s)",  # noqa: E501
            )

    await repo.delete_existing(account_person)
    return account_person


add_crud_endpoints(
    router=router,
    model=AccountPerson,
    slug="",
    name_prefix="account:people",
    repo_cls=RequestBoundRepository,
    crud=CRUD_CREATE | CRUD_UPDATE | CRUD_DELETE,
    route_tags=[route_tags.CRUD_PORT_MAY_2021],
    require_permissions=Permission.MARKET,
    exlude_none_on_update=False,
)


@router.post(
    "/{person_id}/identifiers",
    name="account:people:assign-identifier-to",
    description="Assign one or more identifiers to an account person.",
)
async def assign_identifier_to_account_person(
    person_id: str,
    request: Request,
    body: Union[PersonIdentifiersIn, IdentifierIn] = Body(...),
    mb: FastApiMessageBus = Depends(),
):
    if isinstance(body, IdentifierIn):
        await mb.request(
            AssignIdentifierCommand(
                subject_model=AccountPerson,
                subject_id=person_id,
                identifier=Identifier.from_str(body.identifier),
                request=request,
            )
        )
    else:
        await mb.request(
            AssignPersonIdentifiersCommand(
                subject_model=AccountPerson,
                subject_id=person_id,
                identifiers=body.identifiers,
                request=request,
            )
        )
    return OkResponse()
