# ruff: noqa: E501
import datetime as dt
from dependency_injector.wiring import Provide, inject
from efdh_api.api.request_params import ScrollablePaginatedListParams
from efdh_api.schemas.instruments import PublishInstrumentsRequestBody
from efdh_api.schemas.rics import (
    SubmissionToRicMapping,
    UnmappedInstrument,
    UnmappedInstrumentsPayload,
)
from efdh_api.services.market_data import MarketDataService
from efdh_api.services.ric_currency_repo import RicCurrencyRepo
from efdh_api.services.ric_level2 import RicLevel2Service
from efdh_api.services.ric_mapping import RicMappingService
from efdh_api.services.ric_repo import RicRepository
from efdh_api.services.s3 import S3Service
from efdh_api.utils.containers import Container
from efdh_api.utils.results import EFDHSearchResult
from efdh_utils.schema.refinitiv import RefinitivEventType
from fastapi import APIRouter, BackgroundTasks, Depends, Query
from fastapi.param_functions import Body
from fastapi.params import Path
from pydantic import AnyUrl
from typing import Dict, List, Optional, Set, Union

router = APIRouter()


class S3Uri(AnyUrl):
    allowed_schemes = {"s3"}


# owner: api
@router.get("", tags=["ric"])
@inject
async def get_rics(
    modified_since: Optional[int] = Query(
        None,
        alias="modifiedSince",
        description="Get rics which are modified since this timestamp value",
    ),
    skip: int = Query(0),
    take: int = Query(50),
    repo: RicRepository = Depends(Provide[Container.ric_repository]),
):
    """Gets all rics (Refinitiv's instrument code), (optionally) since a given
    timestamp(optional Supports scrolling by making use of ES search_after
    feature The API uses ES search_after clause to provide a scrolling
    behaviour on the results.

    Due to eventually consistent nature of ES, the API may return inconsistent result, which is expected from
    search_after clause

    The API requires specifying sort parameter when the take parameter is more than 500, so that it can use
    search_after scrolling. If the response contains a non-null nextSearchAfter, it should be used in
    search_after parameter of the next call to get the next page of results. Each search session which uses
    search_after should use the same sort for getting all pages within that search session.

    search_after and sort can be specified, even when take≤500 to scroll through the results

    Whenever search_after is specified (or enabled due to take >= 500), skip parameter should not be specified, as all
    results (capped by take) on every page are returned.
    """
    result = repo.get_rics(modified_since=modified_since, skip=skip, take=take)
    response_result = dict(header=dict(returnedHits=len(result), offset=skip), results=result)
    return response_result


@router.get("/local_ric/{ric}", tags=["ric-level2"])
@inject
async def get_all_by_rics(
    ric: str,
    service: RicLevel2Service = Depends(Provide[Container.ric_level2_service]),
):
    return service.get_all_by_rics(rics=ric)


@router.get(
    "/by-instrument/{instrument_id}",
    tags=["ric"],
)
@inject
async def get_ric_by_instrument(
    instrument_id: str = Path(..., description="An ISIN or Instrument Id"),
    repo: RicRepository = Depends(Provide[Container.ric_repository]),
):
    ric_level1_record = repo.get_ric_by_instrument(instrument_id)
    return dict(header=dict(returnedHits=1), results=[ric_level1_record])


@router.post("/bulk-rics-for-instrument-ids/", name="rics:post-bulk-rics-for-instruments")
@inject
async def get_rics_by_instruments(
    instrument_ids: List[str] = Body(...),
    use_prefix_query: Optional[bool] = Query(default=False, alias="usePrefixQuery"),
    repo: RicRepository = Depends(Provide[Container.ric_repository]),
):
    results = repo.get_rics_by_instruments(
        instrument_ids=instrument_ids,
        use_prefix_query=use_prefix_query,
    )
    return dict(
        hits=results,
        total=len(results),
    )


@router.post(
    "/unmapped-instruments-v2",
    name="rics:post-unmapped-instruments-v2",
    response_model=SubmissionToRicMapping,
)
async def post_unmapped_instrument_ids_v2(
    background_tasks: BackgroundTasks,
    params: ScrollablePaginatedListParams = Depends(),
    unmapped_instruments: List[UnmappedInstrument] = Body(...),
    tenant: Optional[str] = Body(...),
    stack: Optional[str] = Body(...),
    map_all: bool = Query(False, description="Whether to update already mapped instruments"),
    service: RicMappingService = Depends(Provide[Container.ric_mapping_service]),
):
    return service.post_unmapped_instrument_ids_v2(
        background_tasks=background_tasks,
        unmapped_instruments=unmapped_instruments,
        map_all=map_all,
        tenant=tenant,
        stack=stack,
        **params.as_search_kwargs(),
    )


# See ENG-4533 for more details
@router.post(
    "/unmapped-instruments-v3",
    name="rics:post-unmapped-instruments-v3",
    response_model=SubmissionToRicMapping,
)
async def post_unmapped_instrument_ids_v3(
    background_tasks: BackgroundTasks,
    params: ScrollablePaginatedListParams = Depends(),
    payload: UnmappedInstrumentsPayload = Body(...),
    map_all: bool = Query(False, description="Whether to update already mapped instruments"),
    service: RicMappingService = Depends(Provide[Container.ric_mapping_service]),
):
    return service.post_unmapped_instrument_ids_v3(
        background_tasks=background_tasks,
        payload=payload,
        map_all=map_all,
        **params.as_search_kwargs(),
    )


@router.post("/publish", tags=["ric-level2"])
async def publish(
    publish_instruments_request_body: PublishInstrumentsRequestBody = Body(...),
    service: RicLevel2Service = Depends(Provide[Container.ric_level2_service]),
):
    """Creates a LocalRicLookup."""
    created_ric = service.publish(publish_instruments_request_body=publish_instruments_request_body)
    return created_ric


@router.post("/post-bulk-rics-for-keys", tags=["ric-level2"])
async def get_all_level2instrument_by_key(
    keys: Set[str] = Body(...),
    service: RicLevel2Service = Depends(Provide[Container.ric_level2_service]),
):
    return service.get_all_level2instrument_by_key(keys)


@router.get("/{ric}/ticks", tags=["ric"])
async def view_ticks(
    ric: str,
    timestamp: Union[int, dt.datetime],
    event_type: Optional[RefinitivEventType] = Query(
        default=None,
        alias="eventType",
        description="If 'eventType' is not included, both 'Quote' and 'Trade' ticks will be returned",
    ),
    before: Optional[int] = Query(default=50, alias="numberTicksBefore"),
    after: Optional[int] = Query(default=50, alias="numberTicksAfter"),
    service: MarketDataService = Depends(Provide[Container.market_data_service]),
):
    """Fetches ticks occurring immediately before and after the given
    instrument at the given timestamp, including the queried tick in the
    middle.

    Therefore if one queries 50 Ticks before and after, expect 101 ticks
    in total returned.
    """
    result = service.get_before_and_after_ticks(
        ric=ric,
        timestamp_ms=timestamp,
        before=before,
        after=after,
        event_type=event_type,
    )
    return EFDHSearchResult.from_raw_result(result)


@router.post("/bulk-get-currency", tags=["ric-currency"])
@router.post("/currency")
@inject
async def bulk_get_currencies(
    rics: List[str] = Body(...),
    repo: RicCurrencyRepo = Depends(Provide[Container.ric_currency_repo]),
):
    return repo.bulk_get_currencies(rics=rics)


@router.get("/currency_norm_overrides/{extraction_type}", tags=["ric-currency"])
@inject
def get_currency_norm_overrides(
    extraction_type: str, repo: RicCurrencyRepo = Depends(Provide[Container.ric_currency_repo])
) -> Dict:
    return repo.get_currency_norm_overrides(extraction_type=extraction_type)


@router.get("/currency/{ric}", tags=["ric-currency"])
@inject
def get_currency_by_ric(
    ric: str, repo: RicCurrencyRepo = Depends(Provide[Container.ric_currency_repo])
):
    return repo.get_currency_by_ric(ric=ric)


@router.get("/currency/by_list/{list_id}", tags=["ric-currency"])
@inject
def get_currencies_by_list(
    list_id: str,
    level1: bool = Query(default=True),
    repo: RicCurrencyRepo = Depends(Provide[Container.ric_currency_repo]),
):
    return repo.get_currencies_by_list(list_id=list_id, level1=level1)


@router.get("/presigned-url/{s3_uri:path}", name="rics:presigned-url", response_model=str)
@inject
async def get_presigned_url(
    s3_uri: S3Uri, service: S3Service = Depends(Provide[Container.s3_service])
):
    return service.get_presigned_url(s3_uri)
