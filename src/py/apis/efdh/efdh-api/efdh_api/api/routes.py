# ruff: noqa: E501

from efdh_api.api import (
    ecb_euro_ref_rates,
    info,
    instrument_list_monitoring,
    instruments,
    leis,
    mics,
    news,
    order_book,
    perm_ids,
    potam,
    refine_schema,
    ric,
)
from fastapi import APIRouter

efdh_base_router = APIRouter()
efdh_base_router.include_router(info.router)
efdh_base_router.include_router(
    ecb_euro_ref_rates.router, tags=["ECB Euro Ref Rate"], prefix="/ecb-euro-ref-rates"
)
efdh_base_router.include_router(instruments.router, tags=["instruments"], prefix="/instruments")
efdh_base_router.include_router(ric.router, tags=["rics"], prefix="/rics")
efdh_base_router.include_router(refine_schema.router, prefix="/schema", tags=["Refine Schema"])

efdh_base_router.include_router(mics.router, tags=["mics"], prefix="/mics")
efdh_base_router.include_router(order_book.router, tags=["order-book"], prefix="/order-book")

efdh_base_router.include_router(news.router, prefix="/news", tags=["News"])
efdh_base_router.include_router(perm_ids.router, prefix="/perm-ids", tags=["Perm IDs"])
efdh_base_router.include_router(
    instrument_list_monitoring.router, prefix="/instrument-list-monitoring", tags=["ILM"]
)
efdh_base_router.include_router(leis.router, prefix="/leis", tags=["LEIs"])
efdh_base_router.include_router(potam.router, prefix="/potam", tags=["POTAM"])
