# ruff: noqa: E501
import datetime
from efdh_api.utils.efdh_config import EFDHConfig
from pydantic import BaseModel
from reference_db.models.instrument import Instrument
from reference_db.models.ric import Ric
from sqlalchemy import desc, func
from sqlalchemy.future import select
from typing import List


class RicRecordCfi(BaseModel):
    code: str


class RicRecord(BaseModel):
    instrumentUniqueIdentifier: str
    instrumentListId: str | None
    preferredRic: str
    ric: str
    preferredRicCurrency: str | None
    preferredRicVenueRefinitiv: str | None
    cfi: str | RicRecordCfi
    ricCreatedDateTime: datetime.datetime | None = None
    ricUpdatedDateTime: datetime.datetime | None = None
    instrumentCreatedDateTime: datetime.datetime | None = None
    instrumentUpdatedDateTime: datetime.datetime | None = None
    isin: str | None = None


class BulkRicRecord(BaseModel):
    instrumentUniqueIdentifier: str
    isin: str | None = None
    preferredRic: str  # same as ric


class RicRepository:
    def __init__(self, session_factory, config: EFDHConfig) -> None:
        self._session_factory = session_factory
        self._config = config

    def get_rics(
        self, modified_since=None, skip: int = 0, take: int = 50, **params
    ) -> list[RicRecord]:
        records = []
        with self._session_factory() as session:
            qry = (
                select(Ric, Instrument)
                .join(Instrument, Instrument.ric == Ric.ric)
                .where(Ric.isLevel1.is_(True))
                .order_by(desc(Instrument.updatedDateTime))
                .limit(take)
                .offset(skip)
            )
            if modified_since is not None:
                qry = qry.where(Ric.updatedDateTime >= modified_since)

            result = session.execute(qry).all()
            ric: Ric
            instrument: Instrument
            for ric, instrument in result:
                record_cfi = instrument.cfiCode and RicRecordCfi(code=instrument.cfiCode)
                record = RicRecord(
                    instrumentUniqueIdentifier=instrument.instrumentUniqueId,
                    instrumentListId=ric.instrumentListIdL1,
                    preferredRic=ric.ric,
                    ric=ric.ric,
                    preferredRicCurrency=ric.currency,
                    preferredRicVenueRefinitiv=ric.refinitivVenue,
                    cfi=record_cfi,
                    ricCreatedDateTime=ric.createdDateTime,
                    ricUpdatedDateTime=ric.updatedDateTime,
                    instrumentCreatedDateTime=instrument.createdDateTime,
                    instrumentUpdatedDateTime=instrument.updatedDateTime,
                    isin=instrument.isin,
                )
                records.append(record)
        return records

    def get_ric_by_instrument(self, instrument_id) -> RicRecord:
        with self._session_factory() as session:
            qry = (
                select(Ric, Instrument)
                .join(Instrument, Instrument.ric == Ric.ric)
                .where(Ric.isLevel1.is_(True))
                .where(Instrument.instrumentUniqueId == instrument_id)
            )
            result: tuple[Ric, Instrument] = session.execute(qry).one()
            ric, instrument = result
            record_cfi = instrument.cfiCode and RicRecordCfi(code=instrument.cfiCode)
            return RicRecord(
                instrumentUniqueIdentifier=instrument.instrumentUniqueId,
                instrumentListId=ric.instrumentListIdL1,
                preferredRic=ric.ric,
                ric=ric.ric,
                preferredRicCurrency=ric.currency,
                preferredRicVenueRefinitiv=ric.refinitivVenue,
                cfi=record_cfi,
                ricCreatedDateTime=ric.createdDateTime,
                ricUpdatedDateTime=ric.updatedDateTime,
                instrumentCreatedDateTime=instrument.createdDateTime,
                instrumentUpdatedDateTime=instrument.updatedDateTime,
                isin=instrument.isin,
            )

    def get_rics_by_instruments(
        self,
        instrument_ids: List[str],
        use_prefix_query: bool = False,
        skip: int = 0,
        take: int = 50,
    ) -> list[BulkRicRecord]:
        """
        Returns a list of tuple of RIC, Instrument pairs.

        IF use_prefix_query is True, it will search instrument_ids in Instrument.isin, otherwise
        search in Instrument.instrumentUniqueId.
        In case of use_prefix_query, there will be multiple Instruments.instrumentUniqueId per isin.
        We need to take the latest RIC for each group of Instrument.isin, latest by
        Instrument.updatedDateTime. We need to create SQL query that will group by isin, and
        only return single latest Instrument for each isin.
        """
        records = []
        with self._session_factory() as session:
            qry = (
                select(Ric.ric, Instrument.instrumentUniqueId, Instrument.isin)
                .join(Instrument, Instrument.ric == Ric.ric)
                .where(Ric.isLevel1.is_(True))
            )
            if not use_prefix_query:
                # when this flag is not set, we search by instrumentUniqueId
                qry = qry.where(Instrument.instrumentUniqueId.in_(instrument_ids)).order_by(
                    desc(Instrument.updatedDateTime)
                )
            else:
                # when the flag is set, we need to search by isin. For each isin, there will
                # be multiple instrumentUniqueId, so we need to group by isin and take the latest
                # instrumentUniqueId for each isin.
                qry = (
                    qry.distinct(Instrument.isin)
                    .where(Instrument.isin.in_(instrument_ids))
                    .order_by(
                        Instrument.isin,
                        desc(func.coalesce(Instrument.updatedDateTime, Instrument.createdDateTime)),
                        desc(Instrument.id),  # optional but makes ties deterministic
                    )
                )
            for ric, instrumentUniqueId, isin in session.execute(
                qry.limit(take).offset(skip)
            ).all():
                record = BulkRicRecord(
                    instrumentUniqueIdentifier=instrumentUniqueId,
                    isin=isin,
                    preferredRic=ric,
                )
                records.append(record)

        return records
