# ruff: noqa: E501
import base64
import datetime
import logging
import numpy as np
import pandas as pd
import pendulum
import polars as pl
import pyarrow as pa
import pyarrow.compute as pa_pc
from datetime import timedelta
from efdh_api.services.ric_level2 import RicLevel2Service
from efdh_api.utils.results import RawResult
from efdh_api.utils.utils import StringEnum, parse_to_datetime
from efdh_utils.parquet_handlers import (
    get_eod_stats,
    get_level1_rolling_tick_parquet,
    get_level1_tick_parquet,
)
from efdh_utils.schema.parquet import (
    EoDStatsColumns,
    ResolutionLevel1,
    RollingPeriod,
    RollingResolution,
    TradeTickColumns,
)
from efdh_utils.schema.refinitiv import (
    ElektronExtractColumns,
    RefinitivEventType,
    RefinitivExtractColumns,
)
from efdh_utils.statistics import calculate_volatility
from typing import Dict, Optional, Union

log = logging.getLogger(__name__)

MAX_REDUCTION_ATTEMPTS = 5


class CheckType(StringEnum):
    CHECK_DATE = "checkDate"
    TRADE_DATE = "tradeDate"


class ApiPriceColumns:
    ACCUMULATED_ASK_ORDER = "accumulatedAskOrder"
    ACCUMULATED_BID_ORDER = "accumulatedBidOrder"
    ADTV = "adtv"
    ASK_PRICE = "askPrice"
    ASK_SIZE = "askSize"
    BID_PRICE = "bidPrice"
    BID_SIZE = "bidSize"
    BID_ASK_SPREAD = "bidAskSpread"
    CLOSE = "close"
    CLOSE_PRICE = "closePrice"
    COUNT = "count"
    CURRENCY = "currency"
    DATE = "date"
    DATE_TIME_STRING = "ts_as_string"
    EXCHANGE_CODE = "exchangeCode"
    FOUR_WEEK_CHANGE = "fourWeekChange"
    GMT_OFFSET = "gmtOffset"
    HIGH = "high"
    INTEREST = "interest"
    LOW = "low"
    MID_PRICE = "midPrice"
    ONE_WEEK_CHANGE = "oneWeekChange"
    OPEN = "open"
    PRICE = "price"
    RIC_CODE = "ricCode"
    STANDARD_DEVIATION = "std"
    TIMESTAMP = "ts"
    TRADE_CLOSE_DATE_TIME = "tradeCloseDateTime"
    TRADE_COUNT = "tradeCount"
    TRADE_PRICE_CURRENCY = "tradePriceCurrency"
    TRADE_OPEN_DATE_TIME = "tradeOpenDateTime"
    TRADE_VOLUME = "tradeVolume"
    TWELVE_WEEK_CHANGE = "twelveWeekChange"
    VOLUME = "volume"
    VAMP = "vamp"
    VAMP_LOWER_BOUND = "vampLowerBound"
    VAMP_UPPER_BOUND = "vampUpperBound"
    VWAP = "vwap"


QUOTE_COLUMN_MAP = {
    RefinitivExtractColumns.ACCUMULATED_ASK_ORDER: ApiPriceColumns.ACCUMULATED_ASK_ORDER,
    RefinitivExtractColumns.ACCUMULATED_BID_ORDER: ApiPriceColumns.ACCUMULATED_BID_ORDER,
    RefinitivExtractColumns.ASK_PRICE: ApiPriceColumns.ASK_PRICE,
    RefinitivExtractColumns.ASK_SIZE: ApiPriceColumns.ASK_SIZE,
    RefinitivExtractColumns.BID_PRICE: ApiPriceColumns.BID_PRICE,
    RefinitivExtractColumns.BID_SIZE: ApiPriceColumns.BID_SIZE,
    RefinitivExtractColumns.DATE_TIME: ApiPriceColumns.DATE_TIME_STRING,
    RefinitivExtractColumns.GMT_OFFSET: ApiPriceColumns.GMT_OFFSET,
    RefinitivExtractColumns.MID_PRICE: ApiPriceColumns.MID_PRICE,
    RefinitivExtractColumns.RIC: ApiPriceColumns.RIC_CODE,
    "Count": ApiPriceColumns.COUNT,
}
TRADE_COLUMN_MAP = {
    "Close": ApiPriceColumns.CLOSE,
    "Count": ApiPriceColumns.COUNT,
    RefinitivExtractColumns.DATE_TIME: ApiPriceColumns.DATE_TIME_STRING,
    TradeTickColumns.GMT_OFFSET: ApiPriceColumns.GMT_OFFSET,
    TradeTickColumns.HIGH: ApiPriceColumns.HIGH,
    TradeTickColumns.LOW: ApiPriceColumns.LOW,
    TradeTickColumns.MARKET_VWAP: ApiPriceColumns.VWAP,
    TradeTickColumns.OPEN: ApiPriceColumns.OPEN,
    TradeTickColumns.PRICE: ApiPriceColumns.PRICE,
    TradeTickColumns.RIC: ApiPriceColumns.RIC_CODE,
    TradeTickColumns.TRADE_PRICE_CURRENCY: ApiPriceColumns.TRADE_PRICE_CURRENCY,
    TradeTickColumns.VOLUME: ApiPriceColumns.VOLUME,
}
ELEKTRON_COLUMN_MAP = {
    # ElektronExtractColumns.ASK_HIGH: "",
    # ElektronExtractColumns.BID_HIGH:"",
    # ElektronExtractColumns.CURRENCY_CODE:"",
    ElektronExtractColumns.TRADE_DATE: ApiPriceColumns.DATE,
    # ElektronExtractColumns.EXCHANGE_CODE:"",
    ElektronExtractColumns.HIGH: ApiPriceColumns.HIGH,
    ElektronExtractColumns.LOW: ApiPriceColumns.LOW,
    # ElektronExtractColumns.LOW_ASK:"",
    # ElektronExtractColumns.LOW_BID:"",
    ElektronExtractColumns.MARKET_VWAP: ApiPriceColumns.VWAP,
    ElektronExtractColumns.OPEN: ApiPriceColumns.OPEN,
    ElektronExtractColumns.OPEN_ASK: ApiPriceColumns.ASK_SIZE,
    ElektronExtractColumns.OPEN_BID: ApiPriceColumns.BID_SIZE,
    # ElektronExtractColumns.OPEN_INTEREST:"",
    ElektronExtractColumns.RIC: ApiPriceColumns.RIC_CODE,
    # ElektronExtractColumns.TRADE_DATE:"",
    ElektronExtractColumns.UNIVERSAL_ASK_PRICE: ApiPriceColumns.ASK_PRICE,
    ElektronExtractColumns.UNIVERSAL_BID_PRICE: ApiPriceColumns.BID_PRICE,
    ElektronExtractColumns.UNIVERSAL_CLOSE_PRICE: ApiPriceColumns.CLOSE_PRICE,
    ElektronExtractColumns.VOLUME: ApiPriceColumns.VOLUME,
    EoDStatsColumns.TRADE_VOLUME: ApiPriceColumns.TRADE_VOLUME,
    EoDStatsColumns.OPEN_PRICE: ApiPriceColumns.OPEN,
    EoDStatsColumns.CLOSE_PRICE: ApiPriceColumns.CLOSE,
}

EOD_COLUMN_MAP = {
    EoDStatsColumns.CLOSE_ASK_PRICE: ApiPriceColumns.ASK_PRICE,
    EoDStatsColumns.CLOSE_BID_PRICE: ApiPriceColumns.BID_PRICE,
    EoDStatsColumns.CLOSE_PRICE: ApiPriceColumns.CLOSE,
    EoDStatsColumns.CURRENCY: ApiPriceColumns.CURRENCY,
    EoDStatsColumns.DATE: ApiPriceColumns.DATE,
    EoDStatsColumns.EXCHANGE_CODE: ApiPriceColumns.EXCHANGE_CODE,
    # EoDStatsColumns.HIGH_ASK_PRICE: ApiPriceColumns.ASK_PRICE,
    # EoDStatsColumns.HIGH_BID_PRICE: ApiPriceColumns.BID_PRICE,
    EoDStatsColumns.HIGH_PRICE: ApiPriceColumns.HIGH,
    # EoDStatsColumns.LOW_ASK_PRICE: ApiPriceColumns.ASK_PRICE,
    # EoDStatsColumns.LOW_BID_PRICE: ApiPriceColumns.BID_PRICE,
    EoDStatsColumns.LOW_PRICE: ApiPriceColumns.LOW,
    EoDStatsColumns.MARKET_VWAP: ApiPriceColumns.VWAP,
    # EoDStatsColumns.OPEN_ASK_PRICE: ApiPriceColumns.ASK_PRICE,
    # EoDStatsColumns.OPEN_BID_PRICE: ApiPriceColumns.BID_PRICE,
    EoDStatsColumns.OPEN_INTEREST: ApiPriceColumns.INTEREST,
    EoDStatsColumns.OPEN_PRICE: ApiPriceColumns.OPEN,
    EoDStatsColumns.TRADE_VOLUME: ApiPriceColumns.VOLUME,
}

# Date from which market data is guaranteed to exist to customers.
MIN_MARKET_DATA_DATE = pendulum.datetime(2020, 1, 1)


def _verify_b64(instrument_id):
    try:
        decoded = base64.b64decode(instrument_id, validate=True)
        decoded_str = decoded.decode()

        # Ensure re-encoding matches to avoid any accidental decoding
        if base64.b64encode(decoded).decode() == instrument_id:
            return decoded_str

    except Exception:
        pass

    log.info(f"Decoded Instrument Is: {instrument_id}")
    return instrument_id  # Return the original instrument_id if decoding fails


class MarketDataService:
    def __init__(self, ric_level2_service: RicLevel2Service):
        self._ric_level2_service = ric_level2_service

    def _parse_rolling_price_response(
        self,
        df: pd.DataFrame,
        column_map: Dict,
        number_of_points: Optional[int] = None,
        use_eod_stats: bool = False,
    ) -> RawResult:
        # add the epoch timestamp
        if df is None or df.empty:
            return RawResult(
                **{
                    "took": 0,
                    "timed_out": False,
                    "hits": {"hits": [], "total": {"value": 0, "relation": "eq"}},
                }
            )

        date_column = (
            ElektronExtractColumns.TRADE_DATE
            if use_eod_stats
            else RefinitivExtractColumns.DATE_TIME
        )

        if date_column not in df.columns and "Date" in df.columns:
            date_column = "Date"

        if use_eod_stats:
            df[ApiPriceColumns.PRICE] = df[RefinitivExtractColumns.CLOSE_PRICE]

        # use milliseconds ts
        df[ApiPriceColumns.TIMESTAMP] = df[date_column].apply(
            lambda t: int(parse_to_datetime(t).timestamp() * 1e3)
        )

        # format to ISO 8601 string with UTC timezone, and nanoseconds precision
        df[date_column] = df[date_column].apply(lambda t: pd.to_datetime(t, utc=True).isoformat())

        raw_total = df.shape[0]

        data = df.sort_values(by=[ApiPriceColumns.TIMESTAMP]).reset_index()

        if not use_eod_stats and number_of_points is not None and raw_total > number_of_points:
            step = len(data) / number_of_points
            selected_indices = [int(round(i * step)) for i in range(number_of_points)]
            selected_indices = [min(idx, len(data) - 1) for idx in selected_indices]
            data = data.iloc[selected_indices].reset_index(drop=True)

        hits = data.replace({np.nan: None}).rename(columns=column_map).to_dict(orient="records")

        return RawResult(
            **{
                "took": 0,
                "timed_out": False,
                "hits": {"hits": hits, "total": {"value": raw_total, "relation": "eq"}},
            }
        )

    def _get_ticks(
        self,
        timestamp_ms: int,
        ric: str,
        before: int,
        after: int,
        event_type: RefinitivEventType,
    ) -> list:
        # check if instrument_id is a valid b64 or not

        date = parse_to_datetime(timestamp_ms)
        target_timestamp_ns = int(timestamp_ms * 1e6)
        # timestamps aligned to day boundaries
        from_timestamp_ns = int(
            datetime.datetime.combine(
                date, datetime.time.min, tzinfo=datetime.timezone.utc
            ).timestamp()
            * 1e9
        )
        to_timestamp_ns = int(
            datetime.datetime.combine(
                date, datetime.time.max, tzinfo=datetime.timezone.utc
            ).timestamp()
            * 1e9
        )
        pa_dt_batches = get_level1_tick_parquet(
            ric=ric,
            from_timestamp_ns=from_timestamp_ns,
            to_timestamp_ns=to_timestamp_ns,
            event_type=event_type,
            resolution=ResolutionLevel1.TICK,
            columns=[TradeTickColumns.DATE_TIME],
        )
        if not pa_dt_batches:
            return list()

        # convert to polars and sort by DT
        pl_table = pl.from_arrow(pa.Table.from_batches(pa_dt_batches)).sort(
            TradeTickColumns.DATE_TIME
        )
        if pl_table.is_empty():
            return list()
        # find the index of row, which is nearest to the target timestamp
        target_idx = (pl_table[TradeTickColumns.DATE_TIME] - target_timestamp_ns).abs().arg_min()

        # Calculate the range of indices for rows before and after the target row
        before_idx = max(0, target_idx - before)
        after_idx = min(pl_table.height - 1, target_idx + after)

        from_timestamp_ns = pl_table[before_idx, TradeTickColumns.DATE_TIME]
        to_timestamp_ns = pl_table[after_idx, TradeTickColumns.DATE_TIME]

        target_tick_batches = get_level1_tick_parquet(
            ric=ric,
            from_timestamp_ns=from_timestamp_ns,
            to_timestamp_ns=to_timestamp_ns,
            event_type=event_type,
            resolution=ResolutionLevel1.TICK,
        )

        final_batches = []
        for batch in target_tick_batches:
            # add a ts column with the timestamp in milliseconds
            a_batch = batch.append_column(
                ApiPriceColumns.TIMESTAMP,
                pa.array(
                    [
                        int(parse_to_datetime(x.as_py()).timestamp() * 1e3)
                        for x in batch[RefinitivExtractColumns.DATE_TIME]
                    ]
                ),
            )
            dt_column_index = a_batch.schema.get_field_index(RefinitivExtractColumns.DATE_TIME)
            a_batch = a_batch.set_column(
                dt_column_index,
                RefinitivExtractColumns.DATE_TIME,
                pa.array(
                    [
                        pd.to_datetime(x.as_py(), utc=True).isoformat()
                        for x in batch[RefinitivExtractColumns.DATE_TIME]
                    ]
                ),
            )
            final_batches.append(a_batch)

        if not final_batches:
            return []
        return pa.Table.from_batches(final_batches).to_pylist()

    def get_before_and_after_ticks(
        self,
        timestamp_ms: int,
        before: int,
        after: int,
        event_type: RefinitivEventType | None,
        instrument_id: Optional[str] = None,
        ric: Optional[str] = None,
    ):
        if event_type and event_type not in {RefinitivEventType.TRADE, RefinitivEventType.QUOTE}:
            raise ValueError(f"Invalid event type: {event_type}")
        if not any([instrument_id, ric]):
            raise ValueError("One of 'instrument_id' or 'ric' must be provided")
        if instrument_id:
            instrument_id = _verify_b64(instrument_id)
            instrument = self._ric_level2_service.get_level1rics_by_instrument_id(instrument_id)[0]
            ric = instrument.ric

        if not event_type:
            quote_hits = self._get_ticks(
                ric=ric,
                timestamp_ms=timestamp_ms,
                before=before,
                after=after,
                event_type=RefinitivEventType.QUOTE,
            )
            trade_hits = self._get_ticks(
                ric=ric,
                timestamp_ms=timestamp_ms,
                before=before,
                after=after,
                event_type=RefinitivEventType.TRADE,
            )
            hits = sorted(
                quote_hits + trade_hits,
                key=lambda k: k[ApiPriceColumns.TIMESTAMP],
            )
            # TODO: add auctions
        else:
            hits = self._get_ticks(
                ric=ric,
                timestamp_ms=timestamp_ms,
                before=before,
                after=after,
                event_type=event_type,
            )

        return RawResult(
            **{
                "took": 0,
                "timed_out": False,
                "hits": {"hits": hits, "total": {"value": len(hits), "relation": "eq"}},
            }
        )

    def get_rolling_prices(
        self,
        instrument_id: str,
        from_: int | datetime.date | datetime.datetime,
        to_: int | datetime.date | datetime.datetime,
        rolling_period: RollingPeriod = RollingPeriod.YEAR,
        event_type: RefinitivEventType | None = None,
        rolling_resolution: RollingResolution = RollingResolution.MINUTE,
        extend: bool = False,
        number_of_points: Optional[int] = None,
        use_eod_stats: bool = False,
    ) -> RawResult:
        if event_type and event_type not in {RefinitivEventType.TRADE, RefinitivEventType.QUOTE}:
            raise ValueError(f"Invalid event type: {event_type}")

        instrument_id = _verify_b64(instrument_id)
        instrument = self._ric_level2_service.get_level1rics_by_instrument_id(instrument_id)[0]
        ric = instrument.ric

        from_parsed = parse_to_datetime(from_, tz=datetime.timezone.utc)
        to_parsed = parse_to_datetime(to_, tz=datetime.timezone.utc)

        if not all([from_parsed, to_parsed]):
            from_parsed = datetime.datetime.now(tz=datetime.timezone.utc) - timedelta(7)
            to_parsed = datetime.datetime.now(tz=datetime.timezone.utc) + timedelta(1)
        elif not from_parsed:
            # To was provided without a start date, only allow previous week
            from_parsed = to_parsed - timedelta(7)
        elif not to_parsed:
            to_parsed = from_parsed + timedelta(7)

        if extend:
            from_timestamp = pd.Timestamp(from_parsed) + pd.offsets.BDay(-1)
            to_timestamp = pd.Timestamp(to_parsed) + pd.offsets.BDay(1)

            from_parsed = to_timestamp.to_pydatetime()
            to_parsed = from_timestamp.to_pydatetime()

        time_delta = to_parsed - from_parsed

        if time_delta.days > 1:
            log.warning(
                f"Time delta is greater than 1 day: {time_delta.days}, changing resolution to HOUR"
            )
            rolling_resolution = RollingResolution.HOUR

        if use_eod_stats:
            pa_table = get_eod_stats(
                ric=ric,
                date_from=from_parsed.date(),
                date_to=to_parsed.date(),
                direct_efdh_access=True,
            )
            column_map = EOD_COLUMN_MAP
        else:
            # filter rolling ticket parquet by ns datetime
            pa_datetime_filter_compute_expression = (
                pa_pc.field(RefinitivExtractColumns.DATE_TIME) >= int(from_parsed.timestamp() * 1e9)
            ) & (pa_pc.field(RefinitivExtractColumns.DATE_TIME) <= int(to_parsed.timestamp() * 1e9))
            if event_type is None:
                pa_table = get_level1_rolling_tick_parquet(
                    rolling_period=rolling_period,
                    rolling_resolution=rolling_resolution,
                    event_type=RefinitivEventType.TRADE,
                    ric=ric,
                    direct_efdh_access=True,
                    pa_compute_expression=pa_datetime_filter_compute_expression,
                )
                column_map = TRADE_COLUMN_MAP
                if pa_table.num_rows == 0:
                    pa_table = get_level1_rolling_tick_parquet(
                        rolling_period=rolling_period,
                        rolling_resolution=rolling_resolution,
                        event_type=RefinitivEventType.QUOTE,
                        ric=ric,
                        direct_efdh_access=True,
                        pa_compute_expression=pa_datetime_filter_compute_expression,
                    )
                    column_map = QUOTE_COLUMN_MAP

            else:
                pa_table = get_level1_rolling_tick_parquet(
                    event_type=event_type,
                    rolling_period=rolling_period,
                    rolling_resolution=rolling_resolution,
                    ric=ric,
                    direct_efdh_access=True,
                    pa_compute_expression=pa_datetime_filter_compute_expression,
                )
                column_map = (
                    QUOTE_COLUMN_MAP if event_type == RefinitivEventType.QUOTE else TRADE_COLUMN_MAP
                )

        return self._parse_rolling_price_response(
            df=pa_table.to_pandas(),
            column_map=column_map,
            number_of_points=number_of_points,
            use_eod_stats=use_eod_stats,
        )

    def get_eod_stats_with_volatility(
        self,
        instrument_id: str,
        from_ts: int,
        to_ts: int,
        standard_deviations: Optional[float],
        check_type: Optional[CheckType],
        trade_date: Optional[datetime.date],
        extend: bool = False,
    ) -> RawResult:
        instrument_id = _verify_b64(instrument_id)
        instrument = self._ric_level2_service.get_level1rics_by_instrument_id(instrument_id)[0]
        ric = instrument.ric

        date_from = parse_to_datetime(from_ts, tz=datetime.timezone.utc).date()
        date_to = parse_to_datetime(to_ts, tz=datetime.timezone.utc).date()

        if extend:
            date_from = (pd.Timestamp(date_from) - pd.offsets.BDay(1)).date()
            date_to = (pd.Timestamp(date_to) + pd.offsets.BDay(1)).date()

        pa_table = get_eod_stats(
            ric=ric,
            date_from=date_from,
            date_to=date_to,
            direct_efdh_access=True,
        )
        df = pa_table.to_pandas()

        if df is None or df.empty:
            return RawResult(
                **{
                    "took": 0,
                    "timed_out": False,
                    "hits": {"hits": [], "total": {"value": 0, "relation": "eq"}},
                }
            )

        # convert date string column to milliseconds timestamp
        df[ApiPriceColumns.TIMESTAMP] = df[EoDStatsColumns.DATE].apply(
            lambda x: int(parse_to_datetime(x).timestamp() * 1000)
        )

        # add ADTV from existing column
        df[ApiPriceColumns.ADTV] = df[EoDStatsColumns.TRADED_VOLUME_20D_EMA]

        if standard_deviations:
            # TODO: this can removed, whenever we re-introduce the pre-computed volatility column
            df[ApiPriceColumns.VAMP] = calculate_volatility(
                market_data=df,
                data_column=EoDStatsColumns.CLOSE_PRICE,
                volatility_window=10,
                min_periods=2,
                volatility_column=ApiPriceColumns.VAMP,
            )

            if check_type == CheckType.TRADE_DATE and trade_date is not None:
                date_mask = df[EoDStatsColumns.DATE] == trade_date.strftime("%Y-%m-%d")

                if date_mask.any():
                    vamp_on_trade_date = df.loc[date_mask, ApiPriceColumns.VAMP].item()
                    df.loc[:, ApiPriceColumns.VAMP] = vamp_on_trade_date
                else:
                    df.loc[:, ApiPriceColumns.VAMP] = np.nan

            df[ApiPriceColumns.STANDARD_DEVIATION] = (
                standard_deviations * df[ApiPriceColumns.VAMP]
            ) / 100.0
            df[ApiPriceColumns.VAMP_LOWER_BOUND] = df[EoDStatsColumns.CLOSE_PRICE] * (
                1 - df[ApiPriceColumns.STANDARD_DEVIATION]
            )
            df[ApiPriceColumns.VAMP_UPPER_BOUND] = df[EoDStatsColumns.CLOSE_PRICE] * (
                1 + df[ApiPriceColumns.STANDARD_DEVIATION]
            )

        hits = (
            df.replace({np.nan: None})
            .sort_values(by=[ApiPriceColumns.TIMESTAMP])
            .rename(columns=ELEKTRON_COLUMN_MAP)
            .to_dict(orient="records")
        )

        return RawResult(
            **{
                "took": 0,
                "timed_out": False,
                "hits": {"hits": hits, "total": {"value": len(hits), "relation": "eq"}},
            }
        )

    @staticmethod
    def _format_pendulum_dt_to_unix_timestamp(val):
        # noinspection PyBroadException
        try:
            if isinstance(val, pd.DatetimeIndex):
                return val.astype(np.int64) // 10**6
            elif type(val) is type(pendulum) or isinstance(val, pd.Timestamp):
                return int(pendulum.instance(val).format("x"))

            return val
        except Exception:
            return val

    def is_pricing_data_available(
        self,
        instrument_id: str,
        from_: Union[int, datetime.date, datetime.datetime],
        to_: Union[int, datetime.date, datetime.datetime],
    ) -> bool:
        # TODO - utilize from/to dates
        # check if instrument_id is a valid b64 or not
        instrument_id = _verify_b64(instrument_id)
        instruments = self._ric_level2_service.get_level1rics_by_instrument_id(instrument_id)
        return len(instruments) >= 1

    def get_min_max(self, instrument_id: str) -> Optional[Dict]:
        # check if instrument_id is a valid b64 or not
        # This API is deprecated and will be removed in the future. The coverage feature is
        # extinct since long time. Setting these to None, which was happening until now
        return {
            "min_date": None,
            "max_date": None,
        }
