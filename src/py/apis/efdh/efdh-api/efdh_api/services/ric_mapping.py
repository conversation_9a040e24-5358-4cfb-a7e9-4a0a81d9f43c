# ruff: noqa: E501

import boto3
import datetime
import fastapi
import logging
import numpy as np
import pandas as pd
import tempfile
from efdh_api.schemas.instruments import (
    InstrumentRequest,
    InstrumentType,
    PublishInstrumentsRequestBody,
)
from efdh_api.schemas.rics import (
    JobSubmissionStatusEnum,
    RicMapSubmission,
    SubmissionToRicMapping,
    UnmappedInstrument,
    UnmappedInstrumentsPayload,
)
from efdh_api.services.instrument_repository import InstrumentsRepository
from efdh_api.services.ric_level2 import RicLevel2Service
from efdh_api.services.ric_repo import BulkRicRecord, RicRepository
from efdh_api.services.static import (
    MAP_L1_L2_FIELDS,
    MAP_UNMAPPED_INSTRUMENTS,
    MAP_UNMAPPED_INSTRUMENTS_V3,
    DFColumns,
    DFColumnsUnmappedInstrumentsV3,
    InstrumentBatchColumns,
    UnmappedInstrumentColumns,
    UnmappedInstrumentColumnsV3,
)
from efdh_api.utils.efdh_config import EFDHConfig
from pathlib import Path
from typing import List, Optional, Set, Tuple

pd.set_option("chained_assignment", None)
REFINITIV_MAPPING_S3_KEY: str = (
    "flows/reference-refinitiv-map-ric/{date}/batch-{stack}-{tenant}-{now}-{i}.csv"
)
QLIK_DATE_FORMAT = "%Y%m%d-%H%M%S%f"
QLIK_DATE_FORMAT_DAY = "%Y%m%d"
MARKET_DATA_VISIBILITY_MAPPING_S3_KEY = (
    "reports/instrumentVisibility/{date}/batch-{stack}-{tenant}-{now}.csv"
)

logger = logging.getLogger(__name__)


def _produce_csv_batch_file(batch_dir: Path, batch: pd.DataFrame, batch_num: int) -> Path:
    batch_file_path = batch_dir.joinpath(f"batch_{batch_num}.csv")
    batch.to_csv(batch_file_path, index=False, encoding="utf-8", sep=",")
    return batch_file_path


def _filter_batch_columns(batch: pd.DataFrame, expected_columns_list: List[str]) -> pd.DataFrame:
    """
    :param expected_columns_list: expected columns to be on the dataframe

    Discard any column that is not expected in the batch. Add missing expected columns with NA values.
    """

    columns_diff = set(expected_columns_list) - set(batch.columns)
    batch[list(columns_diff)] = pd.NA

    return batch[expected_columns_list]


def _submit_instruments__ric_mapping(
    instruments_to_map: pd.DataFrame,
    config: EFDHConfig,
    s3_client,
    instrument_column: Optional[str] = UnmappedInstrumentColumns.INSTRUMENT_ID,
    stack: Optional[str] = "master-data",
    tenant: Optional[str] = "master-data",
    expected_columns_list: Optional[List[str]] = InstrumentBatchColumns.ALL_COLUMNS,
) -> List[RicMapSubmission]:
    """Function to submit the unmapped instruments for RIC mapping.

    :param instruments_to_map: Dataframe to be uploaded into s3
    :param config: Env vars config
    :param s3_client: s3 cliente built by boto3
    :param instrument_column: column used to select the instrument id
    :param stack: Optional, stack name
    :param tenant: Optional, tenant name
    :param expected_columns_list: expected columns to be on the dataframe

    :returns: A list with the succeeded and failled uploads
    """

    instruments_to_map = instruments_to_map.dropna(subset=[DFColumns.REQUEST_INSTRUMENT_UNIQUE_ID])

    return batching_file_for_upload(
        instruments_to_map=instruments_to_map,
        s3_client=s3_client,
        config_size=config.map_ric_batch_size,
        config_s3_key=REFINITIV_MAPPING_S3_KEY,
        config_bucket=config.level1_tick_data_bucket,
        stack=stack,
        tenant=tenant,
        expected_columns_list=expected_columns_list,
        instrument_column=instrument_column,
    )


def _submit_instruments__market_visibility(
    instruments_to_upload: List[UnmappedInstrument],
    config: EFDHConfig,
    s3_client,
    instrument_column: Optional[str] = UnmappedInstrumentColumns.INSTRUMENT_ID,
    stack: Optional[str] = "master-data",
    tenant: Optional[str] = "master-data",
) -> List[RicMapSubmission]:
    """Function to submit the unmapped instruments to s3 to increase the market
    data visibility sent by the clients.

    :param instruments_to_map: Dataframe to be uploaded into s3
    :param config: Env vars config
    :param s3_client: s3 cliente built by boto3
    :param instrument_column: column used to select the instrument id
    :param stack: Optional, stack name
    :param tenant: Optional, tenant name

    :returns: A list with the succeeded and failled uploads
    """

    instruments_to_upload = pd.DataFrame(
        [pd.Series(instrument.dict()) for instrument in instruments_to_upload]
    )

    now = datetime.datetime.now(tz=datetime.timezone.utc).strftime(QLIK_DATE_FORMAT)

    temp_dir = Path(tempfile.mkdtemp())

    today_date = datetime.datetime.now(tz=datetime.timezone.utc).strftime(QLIK_DATE_FORMAT_DAY)

    s3_key = MARKET_DATA_VISIBILITY_MAPPING_S3_KEY.format(
        date=today_date, stack=stack, tenant=tenant, now=now
    )

    file_path = _produce_csv_batch_file(temp_dir, instruments_to_upload, 0)

    response = upload_to_s3(
        data_to_upload=instruments_to_upload,
        s3_client=s3_client,
        file_path=file_path.as_posix(),
        s3_key=s3_key,
        config_bucket=config.level1_tick_data_bucket,
        instrument_column=instrument_column,
        stack=stack,
        tenant=tenant,
    )

    file_path.unlink()

    return [response]


def batching_file_for_upload(
    instruments_to_map: pd.DataFrame,
    s3_client,
    config_size: int,
    config_s3_key: str,
    config_bucket: str,
    instrument_column: str,
    stack: str,
    tenant: str,
    expected_columns_list: List[str],
) -> List[RicMapSubmission]:
    """Function to submit the unmapped instruments to s3.

    :param instruments_to_map: Dataframe to be uploaded into s3
    :param config_size: Env vars config (size for the batch)
    :param config_s3_key: Env vars config (path key to where save the file)
    :param config_bucket: Env vars config (bucket where the file will be written)
    :param refinitiv: optional, Flag to apply a filter of the columns (to be used for the _submit_instruments__ric_mapping)
    :param s3_client: s3 cliente built by boto3
    :param stack: stack name
    :param tenant: tenant name
    :param expected_columns_list: expected columns to be on the dataframe
    :param instrument_column: column used to select the instrument id

    :returns: A list with the succeeded and failled uploads
    """

    response = []

    temp_dir = Path(tempfile.mkdtemp())
    now_dt = datetime.datetime.now(tz=datetime.timezone.utc)
    now = now_dt.strftime(QLIK_DATE_FORMAT)
    today_date = now_dt.strftime(QLIK_DATE_FORMAT_DAY)

    for batch_num, index in enumerate(range(0, len(instruments_to_map), config_size)):
        s3_key = config_s3_key.format(
            stack=stack, tenant=tenant, date=today_date, now=now, i=batch_num
        )

        batch = instruments_to_map.iloc[index : index + config_size]
        batch = _filter_batch_columns(batch=batch, expected_columns_list=expected_columns_list)

        file_path = _produce_csv_batch_file(temp_dir, batch, batch_num)

        response_upload = upload_to_s3(
            data_to_upload=batch,
            s3_client=s3_client,
            file_path=file_path.as_posix(),
            s3_key=s3_key,
            config_bucket=config_bucket,
            instrument_column=instrument_column,
            stack=stack,
            tenant=tenant,
        )

        response.append(response_upload)

        file_path.unlink()  # avoid local file build-up

    return response


def upload_to_s3(
    data_to_upload: pd.DataFrame,
    s3_client,
    file_path: str,
    s3_key: str,
    config_bucket: str,
    instrument_column: str,
    stack: str,
    tenant: str,
):
    """Function to upload into s3.

    :param data_to_upload: Dataframe to be uploaded into s3
    :param file_path: path where the tem file is written
    :param s3_key: Env vars config (path key to where save the file)
    :param config_bucket: Env vars config (bucket where the file will be written)
    :param s3_client: s3 cliente built by boto3
    :param instrument_column: column used to select the instrument id
    :param stack: stack name
    :param tenant: tenant name

    :returns: A list with the succeeded and failled uploads
    """

    instrument_col = "&id" if "&id" in data_to_upload else instrument_column

    try:
        # Upload file to s3
        s3_client.upload_file(
            Filename=file_path,
            Bucket=config_bucket,
            Key=s3_key,
            ExtraArgs={"ACL": "bucket-owner-full-control"},
        )

    except Exception as error:
        response_upload = RicMapSubmission(
            status=JobSubmissionStatusEnum.FAILURE,
            instrument_ids=data_to_upload.loc[:, instrument_col].tolist(),
        )

        logger.error(
            f"Failed to submit instruments into a file for instrumentIDs: {set(data_to_upload.loc[:, instrument_col].tolist())}. Tenant: {tenant} Stack: {stack}, in {s3_key}"
        )

        logger.exception(f"ERROR produced: {str(error)}", exc_info=True)

    else:
        response_upload = RicMapSubmission(
            status=JobSubmissionStatusEnum.SUBMITTED,
            instrument_ids=data_to_upload.loc[:, instrument_col].fillna(value="None").tolist(),
        )

        logger.info(
            f"Successfully written mapping file for {len(data_to_upload.loc[:, instrument_col].tolist())} instrument_ids. File {s3_key}"
        )
    return response_upload


class RicMappingService:
    def __init__(
        self,
        instruments_repository: InstrumentsRepository,
        config: EFDHConfig,
        ric_repository: RicRepository,
        ric_level2_service: RicLevel2Service,
    ):
        self._config = config
        self._instruments_repository = instruments_repository
        self._ric_repository = ric_repository
        self._ric_level2_service = ric_level2_service

    def post_unmapped_instrument_ids_v2(
        self,
        background_tasks: fastapi.BackgroundTasks,
        unmapped_instruments: List[UnmappedInstrument],
        tenant: Optional[str] = "master-data",
        stack: Optional[str] = "master-data",
        map_all: bool = False,
        **params,
    ) -> SubmissionToRicMapping:
        logger.info("Post unmapped instrument ids endpoint")

        s3_client = boto3.client("s3")
        # Submit files in the background
        background_tasks.add_task(
            _submit_instruments__market_visibility,
            instruments_to_upload=unmapped_instruments,
            config=self._config,
            s3_client=s3_client,
            stack=stack,
            tenant=tenant,
        )

        unmapped_instruments_df, unmapped_instrument_ids = self.process_endpoint_input_list(
            unmapped_instruments
        )

        # Check whether instruments are indeed unmapped
        response: list[BulkRicRecord] = self._ric_repository.get_rics_by_instruments(
            instrument_ids=unmapped_instrument_ids
        )

        if not map_all:
            # Remove instruments already mapped
            already_mapped_instruments = set(
                [
                    item.instrumentUniqueIdentifier
                    for item in response
                    if all(
                        [
                            item.instrumentUniqueIdentifier,
                            item.preferredRic,
                        ]
                    )
                ]
            )
            mask = ~unmapped_instruments_df[DFColumns.REQUEST_INSTRUMENT_UNIQUE_ID].isin(
                already_mapped_instruments
            )
            unmapped_instruments_df = unmapped_instruments_df[mask]
            unmapped_instrument_ids = (
                unmapped_instruments_df[DFColumns.REQUEST_INSTRUMENT_UNIQUE_ID]
                .dropna()
                .unique()
                .tolist()
            )
        logger.info(
            f"Instruments being mapped {len(unmapped_instrument_ids)}:  instrument ids: {unmapped_instrument_ids} "
            f"Tenant: {tenant} Stack: {stack} Map All: {map_all}"
        )

        if unmapped_instruments_df.empty:
            logger.info(
                f"No instruments to be mapped, because unmapped_instruments_df is empty. "
                f"Tenant: {tenant} Stack: {stack}"
            )
            return SubmissionToRicMapping(already_mapped=response, submitted_for_map=[])

        # Fetch instrument data needed for the reference-refinitiv-map-ric flow
        fetched_instruments = self._instruments_repository.search_instruments_by_ids(
            instrument_ids=unmapped_instrument_ids, instrument_types=InstrumentType.ALL
        ).results
        fetched_instruments = pd.json_normalize(
            [instrument.dict(exclude_none=True) for instrument in fetched_instruments]
        )

        if InstrumentBatchColumns.INSTRUMENT_UNIQUE_IDENTIFIER in fetched_instruments.columns:
            fetched_instruments.drop_duplicates(
                InstrumentBatchColumns.INSTRUMENT_UNIQUE_IDENTIFIER, inplace=True
            )
            fetched_instruments.dropna(
                subset=[InstrumentBatchColumns.INSTRUMENT_UNIQUE_IDENTIFIER], inplace=True
            )

            # Merge fetched instrument data with submitted instrument data (to allow mapping of instruments created through fallback)
            mask = unmapped_instruments_df.apply(
                lambda x: x[DFColumns.REQUEST_INSTRUMENT_ID]
                not in fetched_instruments[
                    InstrumentBatchColumns.INSTRUMENT_UNIQUE_IDENTIFIER
                ].tolist(),
                axis=1,
            )
            unmapped_instruments_df = pd.concat(
                [fetched_instruments, unmapped_instruments_df[mask]]
            )

        unmapped_instruments_df["tenant"] = tenant
        unmapped_instruments_df["stack"] = stack

        try:
            unique_submitted_ids = set(
                unmapped_instruments_df.apply(
                    lambda x: x[InstrumentBatchColumns.INSTRUMENT_UNIQUE_IDENTIFIER]
                    if not pd.isnull(x[InstrumentBatchColumns.INSTRUMENT_UNIQUE_IDENTIFIER])
                    else x[DFColumns.REQUEST_INSTRUMENT_ID],
                    axis=1,
                ).fillna(value="None")
            )
        except KeyError:
            unique_submitted_ids = set()

        logger.info("Submitting instrument mapping file to S3")

        # # Submit files in the background
        background_tasks.add_task(
            _submit_instruments__ric_mapping,
            instruments_to_map=unmapped_instruments_df,
            config=self._config,
            s3_client=s3_client,
            instrument_column=UnmappedInstrumentColumns.INSTRUMENT_ID,
            stack=stack,
            tenant=tenant,
        )

        logger.info(
            f"Returning response. Submitted {len(unmapped_instruments_df)} instruments to map ric"
        )
        return SubmissionToRicMapping(
            already_mapped=response, submitted_for_map=unique_submitted_ids
        )

    @staticmethod
    def process_endpoint_input_list(
        unmapped_instrument_list: List[UnmappedInstrument],
    ) -> Tuple[pd.DataFrame, List[str]]:
        """Processes and returns the dataframe and list with messages
        content."""
        unmapped_instruments_df = pd.DataFrame(
            [pd.Series(instrument.dict()) for instrument in unmapped_instrument_list]
        )
        unmapped_instruments_df.rename(columns=MAP_UNMAPPED_INSTRUMENTS, inplace=True)
        unmapped_instruments_df.drop_duplicates(DFColumns.REQUEST_INSTRUMENT_ID, inplace=True)
        unmapped_instruments_df.fillna(value=np.nan, inplace=True)
        unmapped_instrument_ids = list(
            unmapped_instruments_df[DFColumns.REQUEST_INSTRUMENT_UNIQUE_ID]
        )

        return unmapped_instruments_df, unmapped_instrument_ids

    def upload_unmapped_instruments_for_market_visibility(
        self, payload: UnmappedInstrumentsPayload, background_tasks: fastapi.BackgroundTasks
    ):
        required_fields = list(UnmappedInstrument.__annotations__.keys())
        unmapped_instruments = [
            UnmappedInstrument(**instrument.dict(include=set(required_fields)))
            for instrument in payload.unmapped_instruments
        ]

        # Submit files in the background
        background_tasks.add_task(
            _submit_instruments__market_visibility,
            instruments_to_upload=unmapped_instruments,
            config=self._config,
            s3_client=boto3.client("s3"),
            stack=payload.stack,
            tenant=payload.tenant,
        )

    # See ENG-4533 for more details
    def post_unmapped_instrument_ids_v3(
        self,
        background_tasks: fastapi.BackgroundTasks,
        payload: UnmappedInstrumentsPayload,
        map_all: bool = False,
        **params,
    ) -> SubmissionToRicMapping:
        """The goal of this endpoint is to start the MapRic workflow for a set
        of instruments + the associated order-level data.

        Before submitting instruments, we use the
        `bulk_get_rics_filtered` endpoint to determine if the
        instruments are already mapped. If they are, and if `map_all` is
        False, we remove them from the list of instruments to be
        submitted for mapping. Otherwise, if `map_all` is True, we
        submit them again to be re-mapped.
        """

        logger.info("Post unmapped instrument ids endpoint v3")

        self.upload_unmapped_instruments_for_market_visibility(payload, background_tasks)

        if payload.process_level_2:
            try:
                l2_payload = self._process_payload_for_l2(payload=payload)
                self._ric_level2_service.publish(
                    publish_instruments_request_body=PublishInstrumentsRequestBody(
                        instruments=l2_payload
                    )
                )
            except Exception:
                logger.exception("L2 mapping failed")

        unmapped_instrument_ids, unmapped_instruments_df = self.preprocess_data(payload=payload)

        response: list[BulkRicRecord] = self._ric_repository.get_rics_by_instruments(
            instrument_ids=unmapped_instrument_ids
        )

        if not map_all:
            unmapped_instrument_ids, unmapped_instruments_df = (
                self.discard_already_mapped_instruments(
                    response=response, unmapped_instruments_df=unmapped_instruments_df
                )
            )

        logger.info(
            f"Instruments being mapped {len(unmapped_instrument_ids)}:  instrument ids: {unmapped_instrument_ids} "
            f"Tenant: {payload.tenant} Stack: {payload.stack} Map All: {map_all}"
        )

        if unmapped_instruments_df.empty:
            logger.info(
                f"No instruments to be mapped, because all input instruments are already mapped."
                f"Tenant: {payload.tenant} Stack: {payload.stack}"
            )
            return SubmissionToRicMapping(already_mapped=response, submitted_for_map=[])

        unmapped_instruments_df["tenant"] = payload.tenant
        unmapped_instruments_df["stack"] = payload.stack

        unique_submitted_ids = self.get_unique_submitted_ids(
            unmapped_instruments_df=unmapped_instruments_df
        )

        logger.info("Submitting files to S3")
        self.back_ground_tasks_helper(
            background_tasks=background_tasks,
            payload=payload,
            unmapped_instruments_df=unmapped_instruments_df,
        )
        logger.info(
            f"Returning response. Submitted {len(unmapped_instruments_df)} instruments to map ric"
        )

        return SubmissionToRicMapping(
            already_mapped=response, submitted_for_map=list(unique_submitted_ids)
        )

    def _process_payload_for_l2(self, payload: UnmappedInstrumentsPayload):
        """Create an L2 Instrument model using L1 payload."""
        instruments = payload.unmapped_instruments
        # for each instrument in instruments, convert it to master_data_api.schemas.instruments.Instrument type
        l2_instruments = []
        for instrument in instruments:
            unmapped_l2_instrument_raw = {}
            for unmapped_l1_col, unmapped_l2_col in MAP_L1_L2_FIELDS.items():
                unmapped_l2_instrument_raw[unmapped_l2_col] = getattr(instrument, unmapped_l1_col)
            unmapped_l2_instrument = InstrumentRequest(**unmapped_l2_instrument_raw)
            l2_instruments.append(unmapped_l2_instrument)

        return l2_instruments

    def back_ground_tasks_helper(
        self,
        background_tasks: fastapi.BackgroundTasks,
        payload: UnmappedInstrumentsPayload,
        unmapped_instruments_df: pd.DataFrame,
    ):
        """Helper task to facilitate testing."""

        s3_client = boto3.client("s3")

        # Submit files in the background
        background_tasks.add_task(
            _submit_instruments__ric_mapping,
            instruments_to_map=unmapped_instruments_df,
            config=self._config,
            s3_client=s3_client,
            instrument_column=UnmappedInstrumentColumnsV3.INSTRUMENT_ID,
            stack=payload.stack,
            tenant=payload.tenant,
            expected_columns_list=DFColumnsUnmappedInstrumentsV3.to_list(),
        )

    @staticmethod
    def get_unique_submitted_ids(unmapped_instruments_df: pd.DataFrame) -> Set[str]:
        """Returns the set of unique instrument IDs to be submitted for
        mapping.

        We use `"ext.instrumentUniqueIdentifier"` as the main ID, and if
        that is absent we fall back to `"instrumentIdCode"`.
        """
        unique_submitted_ids_series = unmapped_instruments_df.loc[
            :, DFColumnsUnmappedInstrumentsV3.EXT_INSTRUMENT_UNIQUE_IDENTIFIER
        ]
        ext_unique_df_is_null_mask = unique_submitted_ids_series.isnull()
        unique_submitted_ids_series[ext_unique_df_is_null_mask] = unmapped_instruments_df.loc[
            :, DFColumnsUnmappedInstrumentsV3.INSTRUMENT_ID_CODE
        ]
        unique_submitted_ids_series = unique_submitted_ids_series.dropna()
        unique_submitted_ids = set(unique_submitted_ids_series)

        return unique_submitted_ids

    @staticmethod
    def discard_already_mapped_instruments(
        response: list[BulkRicRecord], unmapped_instruments_df: pd.DataFrame
    ) -> Tuple[List[str], pd.DataFrame]:
        """Removes instruments that are already mapped from the list of
        instruments to be submitted for mapping.

        If an instrument `ext.instrumentUniqueIdentifier` matches an
        existing RIC by the `instrumentUniqueIdentifier` field and the
        `preferredRic` field is populated,  then discard the instrument
        as it has been mapped already.
        """

        # Remove instruments already mapped
        already_mapped_instruments = set(
            [
                item.instrumentUniqueIdentifier
                for item in response
                if all([item.instrumentUniqueIdentifier, item.preferredRic])
            ]
        )
        already_mapped_mask = unmapped_instruments_df.loc[
            :, DFColumnsUnmappedInstrumentsV3.EXT_INSTRUMENT_UNIQUE_IDENTIFIER
        ].isin(already_mapped_instruments)

        unmapped_instruments_df = unmapped_instruments_df[~already_mapped_mask]
        unmapped_instrument_ids = (
            unmapped_instruments_df[DFColumns.REQUEST_INSTRUMENT_UNIQUE_ID]
            .dropna()
            .unique()
            .tolist()
        )

        return unmapped_instrument_ids, unmapped_instruments_df

    @staticmethod
    def preprocess_data(payload: UnmappedInstrumentsPayload) -> Tuple[List[str], pd.DataFrame]:
        """Converts the input list of instrument records into a DataFrame,
        renames the payload fields to the schema that the Qlik MapRic workflow
        expects, and returns the DataFrame alongside the list of unique
        instrument IDs."""

        unmapped_instruments_df = pd.DataFrame(
            [pd.Series(instrument.dict()) for instrument in payload.unmapped_instruments]
        )
        unmapped_instruments_df.rename(columns=MAP_UNMAPPED_INSTRUMENTS_V3, inplace=True)
        unmapped_instrument_ids = list(
            unmapped_instruments_df[DFColumnsUnmappedInstrumentsV3.EXT_INSTRUMENT_UNIQUE_IDENTIFIER]
        )
        return unmapped_instrument_ids, unmapped_instruments_df
