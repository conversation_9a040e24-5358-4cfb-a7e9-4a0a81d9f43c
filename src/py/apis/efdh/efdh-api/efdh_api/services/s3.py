# ruff: noqa: E501

import botocore.exceptions
import logging
from efdh_api.utils.exceptions import NotFound
from urllib.parse import urlparse

log = logging.getLogger(__name__)

DEFAULT_PRESIGNED_URL_EXPIRY_SECONDS = 900  # 15 mins


class S3Service:
    def __init__(self, s3_client):
        self._s3_client = s3_client

    def get_presigned_url(
        self,
        s3_uri: str,
        expires_in: int = DEFAULT_PRESIGNED_URL_EXPIRY_SECONDS,
        content_type: str = None,
    ):
        parsed_url = urlparse(s3_uri)
        bucket = parsed_url.netloc
        key = parsed_url.path[1:]
        file_name = key.split("/")[-1]
        try:
            self._s3_client.head_object(Bucket=bucket, Key=key)
            params = {
                "Bucket": bucket,
                "Key": key,
                # required to ensure that browser downloads the file instead of displaying it
                # if content_type is provided, add it to the params, default is binary/octet-stream set by s3
                "ResponseContentDisposition": content_type
                if content_type is not None
                else "attachment",
            }

            resp = self._s3_client.generate_presigned_url(
                ClientMethod="get_object",
                Params=params,
                ExpiresIn=expires_in,
            )
            return resp

        except botocore.exceptions.ClientError as ce:
            if ce.response["Error"]["Code"] == "404":
                raise NotFound("File", file_name)
            raise
