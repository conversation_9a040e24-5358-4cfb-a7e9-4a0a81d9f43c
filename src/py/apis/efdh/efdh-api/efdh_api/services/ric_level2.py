# ruff: noqa: E501

import datetime
import logging
import nanoid
import pandas as pd
from aries_io_event.io_param import IOParamFieldSet
from aries_io_event.model import IOEvent
from aries_io_event.task import TaskFieldSet
from aries_io_event.workflow import WorkflowFieldSet
from aries_utils.kafka_rest import KafkaRestClient
from efdh_api.schemas.instruments import (
    InstrumentMappingResult,
    InstrumentMappingResults,
    InstrumentRequest,
    PublishInstrumentsRequestBody,
)
from efdh_api.utils.efdh_config import EFDHConfig
from efdh_api.utils.exceptions import NotFound
from efdh_api.utils.ric_utils import TENANCY_PERMISSIONS, InstrumentColumns
from fastapi import HTTPException
from master_data_schema.components.instrument.cfi_attributes import CfiAttributes
from master_data_schema.components.instrument.derivative import Derivative
from master_data_schema.components.instrument.instrument_identification import (
    InstrumentIdentification,
)
from master_data_schema.components.instrument.timestamps import Timestamps
from master_data_schema.components.mic.mic import MicFieldSet
from master_data_schema.models.local_ric_lookup.model import LocalRicLookup
from pathlib import Path
from reference_db.models.instrument import Instrument
from reference_db.models.instrument_l2 import Level2Instrument
from reference_db.models.listed_market_data_access import ListedMarketAccess
from reference_db.models.ric import Ric
from sqlalchemy import desc, select
from typing import Any, Dict, List, Optional, Set, Union

logger = logging.getLogger(__name__)
S3_KEY_TEMPLATE = "/lake/ingress/temp/{year}/{month}/{day}/{file_name}"
AUDIT_INSTRUMENT_S3_KEY_TEMPLATE = "/lake/ingress/curated/filtered_instruments/{year}/{month}/MONTHLY_ROLLING_FILTERED_INSTRUMENTS.csv"


class RicLevel2Service:
    # _mic_service: MicService

    # _repo: SearchMixin

    def __init__(self, config: EFDHConfig, session_factory, kafka_client: KafkaRestClient):
        self._config = config
        self._session_factory = session_factory
        self._kafka_client = kafka_client

    def _write_csv(self, local_ric_lookups: List[LocalRicLookup], bucket: str, key: str):
        """Write csv to s3 or local depending on debug."""
        path = f"{bucket}{key}"

        # Check if write to local or to s3
        if self._config.debug and not self._config.level2_obd_data_bucket:
            Path(path).expanduser().parent.mkdir(parents=True, exist_ok=True)
        logger.info(f"Writing csv. Bucket: {bucket} Path {key}")
        pd.json_normalize(
            [
                local_ric_lookup.dict(by_alias=True, exclude={"meta__"})
                for local_ric_lookup in local_ric_lookups
            ]
        ).to_csv(path_or_buf=path, index=False)

    @staticmethod
    def _create_legacy_local_ric_lookup(
        mic: ListedMarketAccess, instrument: InstrumentRequest
    ) -> LocalRicLookup:
        dummy_sha = nanoid.generate(alphabet="abcd1234", size=128)
        record = LocalRicLookup(
            mic=MicFieldSet(
                mic=mic.mic,
                refinitivExchangeCode=mic.refinitivExchangeCode,
                isSourceDerivedOrVenue=mic.isSourceDerivedOrVenue,
                sourceName=mic.sourceName,
            ),
            instrumentIdentification=InstrumentIdentification(
                isin=instrument.isin,
                isinAndMic=instrument.isin + instrument.mic,
                ticker=instrument.ticker,
                exchangeSymbolRoot=instrument.exchangeSymbolRoot,
                instrumentIdType=mic.instrumentIdType,
            ),
            cfi=CfiAttributes(
                attribute1=instrument.cfiAttribute1,
                attribute2=instrument.cfiAttribute2,
                attribute3=instrument.cfiAttribute3,
                attribute4=instrument.cfiAttribute4,
                category=instrument.cfiCategory,
                group=instrument.cfiGroup,
                code=instrument.cfiCode,
            ),
            isActiveInstrument=True,
            isAddedToInstrumentList=False,
            isMapRicAttempted=False,
            timestamps=Timestamps(added=datetime.datetime.now(tz=datetime.timezone.utc)),
            derivative=Derivative(expiryDate=instrument.derivativeExpiryDate)
            if instrument.derivativeExpiryDate
            else None,
            isRicPopulated=False,
            tenantIdSha512=[instrument.tenantIdSha512 if instrument.tenantIdSha512 else dummy_sha],
        )
        # john what is localriclookup

        return record

    def _send_asset_mapping_event(self, bucket: str, key: str):
        trace_id = nanoid.generate()
        workflow = WorkflowFieldSet(
            name="map_ric",
            stack=self._config.stack,
            tenant=self._config.stack,
            trace_id=trace_id,
            start_timestamp=datetime.datetime.now(tz=datetime.timezone.utc),
        )
        s3_uri = f"{bucket}{key}"
        io_param = IOParamFieldSet(params=(dict(instrument_file_uri=s3_uri)))
        task = TaskFieldSet(name="map_ric", version="latest", success=False)

        io_event = IOEvent(workflow=workflow, io_param=io_param, task=task)

        logger.info(
            f"Producing record. Topic: {self._config.map_ric_topic} Filepath: {key} Bucket {bucket}"
        )
        self._kafka_client.send(
            io_event=io_event,
            topic=self._config.map_ric_topic,
            raise_on_connection_error=True,
            raise_on_serder_error=True,
        )

    def publish(
        self, publish_instruments_request_body: PublishInstrumentsRequestBody
    ) -> InstrumentMappingResults:
        """Creates new local ric lookup if it passes checks as documented at ht
        tps://steeleye.atlassian.net/wiki/spaces/PRODUCT/pages/1926922249/2.+Or
        der+Book+Depth+-+Publish+to+mapLocalRic#If-checks-pass-%E2%80%98The-
        great-filter%E2%80%99.

        Returns a list of results, which are in same sequence as
        incoming instruments
        """

        logger.info("Level 2 instrument mapping engaged")

        # great filter check
        # modify this - default this for all tenants
        if publish_instruments_request_body.tenantSubscribedMarketAbuseReports:
            self._check_tenant_permissions(
                publish_instruments_request_body.tenantSubscribedMarketAbuseReports
            )
        new_local_ric_lookups = []
        instrument_mapping_results = InstrumentMappingResults(results=[])
        failed_instruments = []
        for instrument in publish_instruments_request_body.instruments:
            # great filter check
            if not instrument.isin:
                error = "No ISIN"
                logger.warning(error)
                # skip the record as per great filter, do not fail api call
                instrument_mapping_result = InstrumentMappingResult(
                    is_ric_populated=False,
                    mic=instrument.mic,
                    isin=instrument.isin,
                    ticker=instrument.ticker,
                    great_filter_error=error,
                )
                instrument_mapping_results.results.append(instrument_mapping_result)
                failed_instruments.append({**instrument_mapping_result.dict(), **instrument.dict()})
                continue
            # great filter check
            if not instrument.mic:
                error = "No mic"
                logger.warning(error)
                # skip the record as per great filter, do not fail api call
                instrument_mapping_result = InstrumentMappingResult(
                    is_ric_populated=False,
                    mic=instrument.mic,
                    isin=instrument.isin,
                    ticker=instrument.ticker,
                    great_filter_error=error,
                )
                instrument_mapping_results.results.append(instrument_mapping_result)
                failed_instruments.append({**instrument_mapping_result.dict(), **instrument.dict()})
                continue

            try:
                with self._session_factory() as session:
                    q = session.query(ListedMarketAccess).filter(
                        ListedMarketAccess.mic == instrument.mic
                    )  # noqa: E712
                    row: List[ListedMarketAccess] = q.first()
                    if row is None:
                        raise ValueError(f"Invalid/Unsupported mic {instrument.mic}")

                    mic: ListedMarketAccess = row

            except ValueError as e:
                # skip the record as per great filter, do not fail api call
                instrument_mapping_result = InstrumentMappingResult(
                    is_ric_populated=False,
                    mic=instrument.mic,
                    isin=instrument.isin,
                    ticker=instrument.ticker,
                    great_filter_error=str(e),
                )
                instrument_mapping_results.results.append(instrument_mapping_result)
                failed_instruments.append({**instrument_mapping_result.dict(), **instrument.dict()})
                continue

            """
            Fetch isRicPopulated, instrumentIdentification.isin
            based on instrument.isin, instrument.mic, instrument.ticker
            from Level2Instrument
            """
            with self._session_factory() as session:
                q = (
                    session.query(
                        Level2Instrument.ric,
                        Level2Instrument.isin,
                        Level2Instrument.isMapRicAttempted,
                    )
                    .filter(Level2Instrument.isin == instrument.isin)
                    .filter(Level2Instrument.mic == instrument.mic)
                )
                row: Optional[List[Level2Instrument]] = q.first()
                if row is None:
                    logger.info(
                        f"No existing Level2Instrument found. ISIN: {instrument.isin}, MIC: {instrument.mic}"
                    )
                    existing_local_ric_lookup = None

                else:
                    existing_local_ric_lookup: Level2Instrument = row

            if (
                existing_local_ric_lookup
                and existing_local_ric_lookup.ric is not None
                and existing_local_ric_lookup.isMapRicAttempted
            ):
                # return result indicating that it's not a new instrument
                instrument_mapping_results.results.append(
                    InstrumentMappingResult(
                        is_new_instrument=False,
                        is_ric_populated=bool(existing_local_ric_lookup.ric),
                        mic=mic.mic,
                        isin=existing_local_ric_lookup.isin,
                    )
                )
            else:
                # publish the instrument
                new_local_ric_lookup = self._create_legacy_local_ric_lookup(
                    mic=mic, instrument=instrument
                )
                if not existing_local_ric_lookup:
                    new_l2_instrument: Level2Instrument = self._create_new_l2_instrument(
                        instrument=instrument, mic=mic
                    )

                    # Make an entry to L2 DB with the new instrument details
                    with self._session_factory() as session:
                        session.add(new_l2_instrument)
                        session.commit()

                # new instruments require triggering asset mapping flows
                new_local_ric_lookups.append(new_local_ric_lookup)
                instrument_mapping_results.results.append(
                    InstrumentMappingResult(
                        is_new_instrument=bool(not existing_local_ric_lookup),
                        is_ric_populated=False,
                        mic=mic.mic,
                        isin=instrument.isin,
                        ticker=instrument.ticker,
                    )
                )

        todays_date = datetime.datetime.now(tz=datetime.timezone.utc)
        if new_local_ric_lookups:
            file_name = f"{todays_date.strftime('%H%M%S')}__{''.join(nanoid.generate())}"
            csv_s3_key = S3_KEY_TEMPLATE.format(
                year=todays_date.year,
                month=todays_date.month,
                day=todays_date.day,
                file_name=file_name + ".csv",
            )
            csv_s3_key = (
                "~" + csv_s3_key
                if self._config.debug and not self._config.level2_obd_data_bucket
                else csv_s3_key
            )
            self._write_csv(
                new_local_ric_lookups,
                bucket=self._config.level2_obd_data_bucket,
                key=csv_s3_key,
            )
            self._send_asset_mapping_event(
                bucket=self._config.level2_obd_data_bucket, key=csv_s3_key
            )

        if failed_instruments:
            logger.warning(f"Failed instruments: {failed_instruments}")

        logger.info("Level 2 instrument mapping disengaged")

        return instrument_mapping_results

    @staticmethod
    def _check_tenant_permissions(tenantSubscribedMarketAbuseReports: List[str]):
        has_permissions = set(tenantSubscribedMarketAbuseReports) & TENANCY_PERMISSIONS
        if not has_permissions:
            raise HTTPException(status_code=401, detail="Tenant does not have required permissions")

    def get_level1rics_by_instrument_id(self, instrument_id: str) -> list[Ric]:
        with self._session_factory() as session:
            base_query = (
                select(Ric)
                .join(Instrument, Instrument.ric == Ric.ric)
                .filter(Instrument.instrumentUniqueId == instrument_id)
                .order_by(desc(Ric.createdDateTime))
            )
            results = session.execute(base_query)
            rows = results.scalars().all()
            return rows

    def get_all_by_rics(self, rics: Optional[Union[str, Set[str]]]):
        # drop None rics
        rics = {rics} if isinstance(rics, str) else rics
        rics = {ric for ric in rics if ric}
        with self._session_factory() as session:
            q = session.query(Ric).filter(Ric.ric.in_(rics)).filter(Ric.isLevel2 == True)  # noqa: E712

            rows: List[Dict[str, Any]] = q.all()

            if len(rows) == 0:
                logger.error("No rics matched")
                raise HTTPException(status_code=404, detail="RIC(s) not found")

            return rows

    def get_all_level2instrument_by_key(self, keys: Set[str]):
        """Get records with matching keys from `local_ric_lookup` index."""
        results = []
        for chunk in self.chunk_array(array=list({key for key in keys if key}), chunk_size=500):
            try:
                with self._session_factory() as session:
                    q = (
                        session.query(Level2Instrument)
                        .join(Ric, Level2Instrument.ric == Ric.ric)
                        .filter(Level2Instrument.key.in_(chunk))
                        .filter(Ric.isLevel2 == True)  # noqa: E712
                    )

                    rows: List[Dict[str, Any]] = q.all()

                    if len(rows) == 0:
                        logger.error("No rics matched")
                        raise HTTPException(status_code=404, detail="RIC(s) not found")

                    results += rows
            except Exception as e:
                logger.exception(f"Unexpected error {str(e)} in bulk_get_local_rics_filtered...")

        return results

    @staticmethod
    def chunk_array(array: List[Any], chunk_size: int):
        """Yield successive `chunk_size` chunks from `array`."""
        for i in range(0, len(array), chunk_size):
            yield array[i : i + chunk_size]

    def get_level2_instrument(self, key: str, with_ric_only: bool = False) -> Level2Instrument:
        """Retrieve a single LocalRicLookup by key.

        The key in question is `key`, not `_meta.key`. In the
        LocalRicLookup definition, the key is formatted as
        `<VENUE>:<ISIN|TRACKER>` for example `XLON:IE00BF3N6Z78`.
        """
        with self._session_factory() as session:
            q = (
                session.query(Level2Instrument)
                .join(Ric, Level2Instrument.ric == Ric.ric)
                .filter(Level2Instrument.key == key)
                .filter(Ric.isLevel2 == True)  # noqa: E712
            )

            rows = q.all()

            if len(rows) == 0:
                logger.error(f"No rics matched {key}")
                raise NotFound(model=None, id=key)

        return rows[0]

    @staticmethod
    def _audit_failed_instruments(
        instruments: List[dict], monthly_s3_path: str, rolling_s3_path: str
    ):
        """Appends newly failed instruments to audit csv, for monthly all
        instruments are appended and a rolling unique isin ticker and mic file
        is kept."""
        today = datetime.date.today()
        frame = pd.DataFrame([instrument for instrument in instruments])
        frame.loc[:, InstrumentColumns.DATE] = today.isoformat()
        frame = frame.drop(
            columns=[InstrumentColumns.IS_NEW_INSTRUMENT, InstrumentColumns.IS_RIC_POPULATED]
        )
        frame = frame.rename(mapper={"great_filter_error": "filterError"}, axis=1)

        try:
            existing_rolling_frame = pd.read_csv(rolling_s3_path)
            appended_rolling_frame = pd.concat([frame, existing_rolling_frame]).drop_duplicates(
                subset=[InstrumentColumns.ISIN, InstrumentColumns.TICKER, InstrumentColumns.MIC]
            )

            if existing_rolling_frame.shape[0] < appended_rolling_frame.shape[0]:
                appended_rolling_frame.to_csv(rolling_s3_path, index=False)
        except FileNotFoundError:
            frame.to_csv(rolling_s3_path, index=False)

        try:
            monthly_frame = pd.read_csv(monthly_s3_path)
            appended_monthly_frame = pd.concat([monthly_frame, frame])
            appended_monthly_frame.to_csv(monthly_s3_path, index=False)
        except FileNotFoundError:
            frame.to_csv(monthly_s3_path, index=False)

    @staticmethod
    def _create_new_l2_instrument(instrument: InstrumentRequest, mic: ListedMarketAccess):
        return Level2Instrument(
            isin=instrument.isin,
            mic=instrument.mic,
            ticker=instrument.ticker,
            exchangeSymbolRoot=instrument.exchangeSymbolRoot,
            cfiCode=instrument.cfiCode,
            isActiveInstrument=True,
            isMapRicAttempted=False,
            derivativeExpiryDate=instrument.derivativeExpiryDate,
            key=f"{instrument.mic}:{instrument.isin}",
            instrumentIdType=mic.instrumentIdType,
            createdBy="master-data-api-svc",
        )
