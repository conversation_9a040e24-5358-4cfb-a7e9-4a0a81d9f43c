import logging
from efdh_api.schemas.order_book import MAX_OBD_LEVELS
from efdh_api.services.market_data import _verify_b64
from efdh_api.services.mic import MicService
from efdh_api.services.ric_level2 import RicLevel2Service
from efdh_api.utils.exceptions import MoreThanOneRecordError, NotFound
from pydantic import BaseModel

log = logging.getLogger(__name__)


class InstrumentLevel2Info(BaseModel):
    key: str
    ric: str
    mic: str | None
    depth: int


class InstrumentLevel2Service:
    def __init__(
        self,
        ric_level2_service: RicLevel2Service,
        mic_service: MicService,
    ):
        self._ric_level2_service = ric_level2_service
        self._mic_service = mic_service

    def get_instrument_info(self, key: str) -> InstrumentLevel2Info:
        """Retrieve information about an instrument from its key.

        :param key: The instrument key, formed by ``<VENUE>:<ISIN>``
        :returns: an InstrumentInfo
        :raises: NotFound if instrument is not found to exist (key invalid)
        """
        key = _verify_b64(key)
        ric = self._ric_level2_service.get_level2_instrument(key, with_ric_only=True)
        try:
            mic = self._mic_service.get(ric.mic)
        except (NotFound, MoreThanOneRecordError) as e:
            log.warning("Missing MIC data for instrument '%s': %s", key, str(e))
            mic = None

        return InstrumentLevel2Info(
            key=key,
            ric=ric.ric,
            mic=ric.mic,
            depth=mic.levels if mic and mic.levels and type(mic.levels) is int else MAX_OBD_LEVELS,
        )
