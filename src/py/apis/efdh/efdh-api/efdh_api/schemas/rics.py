# ruff: noqa: E501

import datetime
from efdh_api.utils.utils import StringEnum
from pydantic import BaseModel, Extra, Field, ValidationError, root_validator, validator
from typing import Dict, List, Optional


class JobSubmissionStatusEnum(StringEnum):
    """Enum for batch job submission status."""

    FAILURE = "failure"
    SUBMITTED = "submitted"


class RicMapSubmission(BaseModel):
    instrument_ids: List[str] = Field(
        ..., description="The batch of instrument_IDs submitted for mapping"
    )
    stack: Optional[str] = Field("master-data", description="stack where the request was called")
    status: JobSubmissionStatusEnum = Field(..., description="Batch job submission status")
    tenant: Optional[str] = Field(
        "master-data", description="tenant the instruments where called for"
    )


class SubmissionToRicMapping(BaseModel):
    already_mapped: list = Field(
        default_factory=list,
        description="The list of already mapped instruments and their respective mapping.",
    )
    submitted_for_map: List[str] = Field(
        ..., description="List of instrument_IDs that were submitted for mapping"
    )


class RicCoverageTimestamps(BaseModel):
    coverageFrom: datetime.datetime = Field(...)
    coverageTo: datetime.datetime = Field(...)

    @root_validator()
    def validate_from_before_to(cls, values):
        if (
            values.get("coverageFrom")
            and values.get("coverageTo")
            and values["coverageFrom"] > values["coverageTo"]
        ):
            raise ValueError(
                f"coverage_from {values['coverageFrom']} cannot be later than coverage_to {values['coverageTo']}"
            )
        return values

    @validator("coverageFrom", "coverageTo")
    def validate_dates_utc(cls, v, field):
        if v and v.utcoffset() and v.utcoffset().total_seconds() != 0:
            raise ValueError(f"{field.name} {v} should be in UTC")
        return v


class RicBroadestCoverage(BaseModel):
    coverageFrom: Optional[datetime.datetime]
    coverageTo: Optional[datetime.datetime]


class BroadestCoverage(BaseModel):
    coverages: Dict[str, RicBroadestCoverage]


class UnmappedInstrument(BaseModel):
    # Only suitable for use in UBS. Submission from other clients requires extra fields
    instrument_id: Optional[str] = Field(
        None, description="ISIN of the instrument 'instrumentDetails.instrument.instrumentIdCode' "
    )
    instrument_unique_identifier: Optional[str] = Field(
        None,
        description=" The unique identifier for the instrument 'instrument.ext.instrumentUniqueIdentifier'",
    )
    instrument_full_name: Optional[str] = Field(
        None,
        description="The name of the instrument 'instrumentDetails.instrument.instrumentFullName'",
    )
    created_through_fallback: Optional[bool] = Field(
        None, description="tag referring to instrument creation"
    )
    issuer_or_operator_trading_venue_id: Optional[str] = Field(None, description="trading venue id")
    price_currency: Optional[str] = Field(None, description="price currency of the instrument")
    source_key: Optional[str] = Field(None, description="source key of the order")

    @root_validator
    def _check_some_value_exists(cls, values):
        # Validate that at least one of the values is not None
        if not any(values.values()):
            raise ValidationError("Some value should be not None")
        return values


# See ENG-4533 for more details
class UnmappedInstrumentV3(BaseModel):
    # Instrument-level fields
    bond_maturity_date: Optional[datetime.date] = Field(None)
    bond_debt_seniority: Optional[str] = Field(None)
    cfi_attribute_1: Optional[str] = Field(None)
    cfi_attribute_2: Optional[str] = Field(None)
    cfi_attribute_3: Optional[str] = Field(None)
    cfi_attribute_4: Optional[str] = Field(None)
    cfi_category: Optional[str] = Field(None)
    cfi_group: Optional[str] = Field(None)
    created_through_fallback: Optional[bool] = Field(
        None, description="tag referring to instrument creation"
    )
    derivative_expiry_date: Optional[datetime.date] = Field(None)
    derivative_option_type: Optional[str] = Field(None)
    derivative_price_multiplier: Optional[float] = Field(None)
    derivative_strike_price: Optional[float] = Field(None)
    derivative_underlying_index_name: Optional[str] = Field(None)
    derivative_underlying_instruments: Optional[List] = Field(None)
    derivative_underlying_index_term: Optional[str] = Field(
        None, description="'instrument.derivative.underlyingIndexTerm'"
    )
    derivative_underlying_index_term_value: Optional[str] = Field(
        None, description="'instrument.derivative.underlyingIndexTermValue'"
    )
    ext_alternative_instrument_id: Optional[str] = Field(None)
    ext_exchange_symbol: Optional[str] = Field(None)
    ext_exchange_symbol_root: Optional[str] = Field(None)
    ext_pricing_references_lxid: Optional[str] = Field(None)
    ext_pricing_references_red_code: Optional[str] = Field(None)
    ext_pricing_references_ric: Optional[str] = Field(None)
    ext_underlying_instruments: Optional[List] = Field(None)
    fx_derivatives_notional_currency_2: Optional[str] = Field(None)
    instrument_classification: Optional[str] = Field(None)
    instrument_full_name: Optional[str] = Field(
        None,
        description="The name of the instrument 'instrumentDetails.instrument.instrumentFullName'",
    )
    instrument_id: Optional[str] = Field(
        None, description="ISIN of the instrument 'instrumentDetails.instrument.instrumentIdCode' "
    )
    instrument_meta_id: Optional[str] = Field(None)
    instrument_meta_key: Optional[str] = Field(None)
    instrument_meta_model: Optional[str] = Field(None)
    instrument_meta_timestamp: Optional[datetime.datetime] = Field(None)
    instrument_meta_user: Optional[str] = Field(None)
    instrument_unique_identifier: str = Field(
        ...,
        description=" The unique identifier for the instrument 'instrument.ext.instrumentUniqueIdentifier'",
    )
    issuer_or_operator_trading_venue_id: Optional[str] = Field(None, description="trading venue id")
    venue_financial_instrument_short_name: Optional[str] = Field(None)
    venue_trading_venue: Optional[str] = Field(None)

    # Order-level fields
    notional_currency_1: Optional[str] = Field(None)
    price_currency: Optional[str] = Field(None, description="price currency of the instrument")
    price_notation: Optional[str] = Field(None)
    source_key: Optional[str] = Field(None, description="source key of the order")
    ultimate_venue: Optional[str] = Field(None)
    venue: Optional[str] = Field(None)
    credit_doc_clause: Optional[str] = Field(None, description="'derivative.creditDocClause'")
    credit_attachment_point: Optional[str] = Field(
        None, description="'derivative.creditAttachmentPoint'"
    )
    credit_detachment_point: Optional[str] = Field(
        None, description="'derivative.creditDetachmentPoint'"
    )

    class Config:
        extra = Extra.ignore


class UnmappedInstrumentsPayload(BaseModel):
    unmapped_instruments: List[UnmappedInstrumentV3] = Field(
        ..., description="List of instrument and possibly order-level data to submit to MapRic"
    )
    tenant: str = Field(..., description="Name of the tenant i.e. `pinafore`")
    stack: str = Field(..., description="Stack of the tenant i.e. `dev-blue`")
    process_level_2: Optional[bool] = Field(
        True, description="True if tenant is subscribed to Level 2 algos"
    )
