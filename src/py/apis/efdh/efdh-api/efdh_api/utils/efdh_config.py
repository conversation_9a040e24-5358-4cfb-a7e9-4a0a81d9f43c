from pydantic import BaseSettings, Field


class EFDHConfig(BaseSettings):
    debug: bool = False
    init_db: bool = False
    se_version: str = "local"
    api_version: str = "v5.0"
    api_prefix: str = f"/api/{api_version}/efdh-api"
    level2_obd_data_bucket: str = "master-data.eu-west-1.steeleye.co"
    level1_tick_data_bucket: str = "refinitiv.steeleye.co"

    efdh_db_url: str = "postgresql://postgres:postgres@localhost:5432/se_reference_db"
    elastic_url: str = "http://localhost:9201"
    elastic_api_key: str | None = None
    elastic_request_timeout: int = 120

    map_ric_batch_size: int = 250
    sentry_url: str = "https://<EMAIL>/6079137"
    sentry_enabled: bool = False
    oauth_client_id: str = "master-data-api-svc"
    oauth_client_secret: str = ""
    oauth_client_url: str = Field(
        default="https://sso.dev-master-data.steeleye.co/auth/realms/master-data/protocol/openid-connect",
    )
    kafka_rest_proxy_url: str = "http://localhost:8282"
    stack: str = "efdh"
    map_ric_topic: str = "aries.map_ric.master-data.events"
    efdh_tenant: str = "efdh"
    logging_config: dict = {
        "version": 1,
        "disable_existing_loggers": False,
        "formatters": {
            "default": {
                "format": "%(asctime)s.%(msecs)03d | %(levelname)s | %(name)s "
                "| [%(funcName)s:%(lineno)d] | %(message)s",
                "datefmt": "%Y-%m-%dT%H:%M:%S",
            }
        },
        "handlers": {
            "console": {
                "class": "logging.StreamHandler",
                "formatter": "default",
            }
        },
        "loggers": {
            "efdh_api": {
                "level": "DEBUG" if debug else "INFO",
                "handlers": ["console"],
                "propagate": True,
            }
        },
    }

    max_coverage_update_attempts: int = 3
    max_tenant_update_attempts: int = 3
